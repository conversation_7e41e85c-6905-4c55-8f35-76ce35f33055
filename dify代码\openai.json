openapi: 3.1.0
info:
  title: FastAPI
  version: 0.1.0
  description: 通用 ComfyUI 生成接口 V2的api
servers:
  - url: 'http://************:8008'
paths:
  /comfy_gen_v2:
    post:
      summary: 通用 ComfyUI 生成 V2
      description: 通用 ComfyUI 生成接口 V2
      operationId: comfy_gen_v2
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ComfyGenV2Req'
        required: true
      responses:
        '200':
          description: Successful Response
          content:
            application/json:
              schema:
                type: object
                title: Response Comfy Gen V2 Comfy Gen V2 Post
        '422':
          description: Validation Error
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HTTPValidationError'
components:
  schemas:
    ComfyGenV2Req:
      properties:
        workflow_name:
          type: string
          title: Workflow Name
          description: 工作流名称
        prompt:
          type: string
          title: Prompt
          description: 生成提示词
        width:
          type: integer
          title: Width
          description: 生成图片宽度
          default: 1024
        height:
          type: integer
          title: Height
          description: 生成图片高度
          default: 1024
        urls:
          anyOf:
            - type: string
            - type: 'null'
          title: Urls
          description: 输入图片URL,多个URL用|分隔
      type: object
      required:
        - workflow_name
        - prompt
      title: ComfyGenV2Req
      description: 通用 ComfyUI 生成请求体 V2
    HTTPValidationError:
      properties:
        detail:
          items:
            $ref: '#/components/schemas/ValidationError'
          type: array
          title: Detail
      type: object
      title: HTTPValidationError
    ValidationError:
      properties:
        loc:
          items:
            anyOf:
              - type: string
              - type: integer
          type: array
          title: Location
        msg:
          type: string
          title: Message
        type:
          type: string
          title: Error Type
      type: object
      required:
        - loc
        - msg
        - type
      title: ValidationError