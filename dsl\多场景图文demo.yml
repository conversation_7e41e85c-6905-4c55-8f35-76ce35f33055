app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 测试api
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/gemini:0.2.1@88c1b2c816ef2ea36fc411b35298a621b3260d34bc08bd9357772092728aadde
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.25@325423749d5e71f8b4681af1b2ed46d7686cb1947e443a86ba5d14ac45ff85a4
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: bowenliang123/md_exporter:1.2.0@b18d95d19f25ed9c73d758048a69cf37c14c1f9f801b354aa7469db44f0df4d6
kind: app
version: 0.3.0
workflow:
  conversation_variables: []
  environment_variables:
  - description: ''
    id: 44e26567-dc7b-4183-8c19-de704dcc4465
    name: text2card
    selector:
    - env
    - text2card
    value: sk-5169
    value_type: string
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      - document
      - audio
      - video
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 30
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 1
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: answer
      id: 1747411980388-source-1747412740633-target
      source: '1747411980388'
      sourceHandle: source
      target: '1747412740633'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1744558984033-source-1747412872687-target
      source: '1744558984033'
      sourceHandle: source
      target: '1747412872687'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1747412872687-source-1747263176464-target
      source: '1747412872687'
      sourceHandle: source
      target: '1747263176464'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1747413659355-source-1747411980388-target
      source: '1747413659355'
      sourceHandle: source
      target: '1747411980388'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: start
        targetType: llm
      id: 1744558984033-source-17474893629220-target
      source: '1744558984033'
      sourceHandle: source
      target: '17474893629220'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: llm
      id: 1747263176464-source-1747413659355-target
      source: '1747263176464'
      sourceHandle: source
      target: '1747413659355'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 17474893629220-source-1747411980388-target
      source: '17474893629220'
      sourceHandle: source
      target: '1747411980388'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: '1744558984033'
      position:
        x: 30
        y: 252
      positionAbsolute:
        x: 30
        y: 252
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.0-flash-exp
          provider: langgenius/gemini/google
        prompt_template:
        - id: 29d09fa7-3dc8-4256-9af4-334a3312c39d
          role: system
          text: ''
        - id: ae5712f3-5bb4-446c-b2ee-50a15f8f46b3
          role: user
          text: '{{#1747412872687.text#}}'
        selected: false
        title: GM
        type: llm
        variables: []
        vision:
          configs:
            detail: high
            variable_selector:
            - sys
            - files
          enabled: true
      height: 89
      id: '1747263176464'
      position:
        x: 638
        y: 252
      positionAbsolute:
        x: 638
        y: 252
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Markdown text
            ja_JP: Markdown text
            pt_BR: Markdown text
            zh_Hans: Markdown格式文本
          label:
            en_US: Markdown text
            ja_JP: Markdown text
            pt_BR: Markdown text
            zh_Hans: Markdown格式文本
          llm_description: ''
          max: null
          min: null
          name: md_text
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: Filename of the output file
            ja_JP: Filename of the output file
            pt_BR: Filename of the output file
            zh_Hans: 输出文件名
          label:
            en_US: Filename of the output file
            ja_JP: Filename of the output file
            pt_BR: Filename of the output file
            zh_Hans: 输出文件名
          llm_description: ''
          max: null
          min: null
          name: output_filename
          options: []
          placeholder: null
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          md_text: ''
          output_filename: ''
        provider_id: bowenliang123/md_exporter/md_exporter
        provider_name: bowenliang123/md_exporter/md_exporter
        provider_type: builtin
        selected: false
        title: Markdown转PDF文件
        tool_configurations: {}
        tool_description: 一个用于将Markdown转换为PDF文件的工具
        tool_label: Markdown转PDF文件
        tool_name: md_to_pdf
        tool_parameters:
          md_text:
            type: mixed
            value: '{{#1747413659355.text#}}'
          output_filename:
            type: mixed
            value: '{{#17474893629220.text#}}'
        type: tool
      height: 53
      id: '1747411980388'
      position:
        x: 1246
        y: 252
      positionAbsolute:
        x: 1246
        y: 252
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1747411980388.files#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 104
      id: '1747412740633'
      position:
        x: 1550
        y: 252
      positionAbsolute:
        x: 1550
        y: 252
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: qwen2.5-7b-instruct
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 99691887-3dff-4443-a313-4da680ed7c41
          role: system
          text: 将用户输入内容翻译成英文，输出且仅输出翻译后的英文内容
        - id: 15833b2d-2421-4de0-b98c-f6c8a85bf513
          role: user
          text: 用户输入待翻译内容：{{#sys.query#}}
        selected: false
        title: 中文指令翻译
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1747412872687'
      position:
        x: 334
        y: 252
      positionAbsolute:
        x: 334
        y: 252
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.2
          mode: chat
          name: qwen2.5-7b-instruct
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 513a0b4e-a9e4-4203-99b8-be7919f6f215
          role: system
          text: '以下documentation 标签内是用户输入的文档内容，你的任务是把文档内容中的英文翻译成中文（图片url链接保持不变），然后保持原始markdown格式输出。


            <documentation>

            {{#1747263176464.text#}}

            </documentation>



            '
        selected: false
        title: 英文内容翻译
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1747413659355'
      position:
        x: 942
        y: 252
      positionAbsolute:
        x: 942
        y: 252
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: 用户输入：{{#sys.query#}}
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 50
        model:
          completion_params:
            temperature: 0.3
          mode: chat
          name: qwen2.5-3b-instruct
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 0ca58818-1dc2-4d90-bba9-24503850c8b5
          role: system
          text: '基于用户输入，构建待生成文档的中文标题，比如：

            用户输入：Instructions with multiple images guide me on how to brew coffee.

            预期输出：咖啡冲煮图文指南'
        selected: false
        title: 文档标题
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '17474893629220'
      position:
        x: 942
        y: 381
      positionAbsolute:
        x: 942
        y: 381
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -69.28080234148035
      y: 18.330088966929054
      zoom: 0.702430223956652
