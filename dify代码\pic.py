import json
import os
import requests
from PIL import Image
from io import BytesIO
import time

def main(arg1: str) -> dict:
    # 解析输入的JSON字符串
    data = json.loads(arg1)
    pic_urls = [item['url'] for item in data['data']]

    # 处理message字段（新arg1格式兼容）
    message = ""
    if 'message' in data:
        # 去除首尾空格和换行符
        message = data['message'].strip().replace('\n', '')


    # 定义存储目录
    storage_dir = "/var/sandbox/sandbox-python/usr/local/storage/download_files"
    if not os.path.exists(storage_dir):
        os.makedirs(storage_dir)

    # 下载图片到本地并获取尺寸
    local_paths = []
    first_image_size = None
    successful_downloads = 0

    for i, url in enumerate(pic_urls[:4]):  # 最多处理4张图片
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                # 使用更精确的时间戳避免文件名冲突
                timestamp = int(time.time() * 1000000)  # 微秒级时间戳
                img_path = os.path.join(storage_dir, f"image_{i+1}_{timestamp}.jpg")

                # 确保文件名唯一
                counter = 0
                while os.path.exists(img_path):
                    counter += 1
                    img_path = os.path.join(storage_dir, f"image_{i+1}_{timestamp}_{counter}.jpg")

                with open(img_path, 'wb') as f:
                    f.write(response.content)

                # 验证图片文件是否有效
                try:
                    test_img = Image.open(img_path)
                    test_img.verify()  # 验证图片完整性
                    test_img.close()

                    local_paths.append(img_path)
                    successful_downloads += 1

                    # 获取第一张成功下载图片的尺寸
                    if first_image_size is None:
                        img = Image.open(img_path)
                        first_image_size = img.size
                        img.close()

                except Exception as img_error:
                    # 图片文件损坏，删除并标记为失败
                    if os.path.exists(img_path):
                        os.remove(img_path)
                    local_paths.append(None)
            else:
                local_paths.append(None)
        except Exception as e:
            local_paths.append(None)

    # 如果没有成功下载任何图片，返回错误但保持结构一致
    if successful_downloads == 0:
        return {
            "pic_urls": pic_urls,
            "pic_url1": pic_urls[0] if len(pic_urls) > 0 else "",
            "pic_url2": pic_urls[1] if len(pic_urls) > 1 else "",
            "pic_url3": pic_urls[2] if len(pic_urls) > 2 else "",
            "pic_url4": pic_urls[3] if len(pic_urls) > 3 else "",
            "combined_image_path": "",
            "markdown_pic_urls": "",
            "message": message,
            "debug_info": {
                "total_urls": len(pic_urls),
                "successful_downloads": 0,
                "processed_images": 0,
                "aspect_ratio": 1.0,
                "layout_type": "error",
                "error": "所有图片下载失败"
            }
        }

    # 如果只有1张有效图片，直接返回原URL不进行拼接
    if successful_downloads == 1:
        # 找到第一张有效图片的URL
        valid_url = ""
        for i, path in enumerate(local_paths):
            if path and os.path.exists(path):
                valid_url = pic_urls[i]
                break

        return {
            "pic_urls": pic_urls,
            "pic_url1": pic_urls[0] if len(pic_urls) > 0 else "",
            "pic_url2": pic_urls[1] if len(pic_urls) > 1 else "",
            "pic_url3": pic_urls[2] if len(pic_urls) > 2 else "",
            "pic_url4": pic_urls[3] if len(pic_urls) > 3 else "",
            "combined_image_path": valid_url,
            "markdown_pic_urls": '\n'.join([f"![image]({url})" for url in pic_urls[:4] if url]),
            "message": message,
            "debug_info": {
                "total_urls": len(pic_urls),
                "successful_downloads": 1,
                "processed_images": 1,
                "aspect_ratio": first_image_size[0] / first_image_size[1] if first_image_size else 1.0,
                "layout_type": "single"
            }
        }

    # 判断图片比例
    aspect_ratio = first_image_size[0] / first_image_size[1] if first_image_size else 1.0
    is_square = 0.67 <= aspect_ratio <= 1.5
    is_landscape = aspect_ratio > 1.5
    is_portrait = aspect_ratio < 0.67

    # 合并图片
    pil_images = []
    margin = 4  # 分割线宽度（每边）

    # 加载图片并调整大小
    if is_square:
        target_size = (512, 512)

        # 根据有效图片数量调整图片尺寸
        if successful_downloads == 3:
            # 3张图片：左1右2布局，右侧两张图片需要调整尺寸
            target_size_right = (512, 256 - margin // 2)  # 右侧图片高度减半

            for i, path in enumerate(local_paths):
                if path and os.path.exists(path):
                    img = Image.open(path)
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    # 第一张图片保持原尺寸，其他图片使用右侧尺寸
                    size = target_size if i == 0 else target_size_right
                    img = img.resize(size, Image.Resampling.LANCZOS)
                    pil_images.append(img)
        else:
            # 2张或4张图片：所有图片使用相同尺寸
            for path in local_paths:
                if path and os.path.exists(path):
                    img = Image.open(path)
                    if img.mode != 'RGB':
                        img = img.convert('RGB')
                    img = img.resize(target_size, Image.Resampling.LANCZOS)
                    pil_images.append(img)
                # 不再添加白色填充图片

        # 根据有效图片数量调整画布和位置
        if successful_downloads == 2:
            # 2张图片：水平排列
            canvas_width = target_size[0] * 2 + margin
            canvas_height = target_size[1]
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),
                (target_size[0] + margin, 0)
            ]
        elif successful_downloads == 3:
            # 3张图片：左1右2布局
            canvas_width = target_size[0] * 2 + margin
            canvas_height = target_size[1]
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),  # 第一张（左侧，完整尺寸）
                (target_size[0] + margin, 0),  # 第二张（右上）
                (target_size[0] + margin, target_size_right[1] + margin)  # 第三张（右下）
            ]
        else:
            # 4张图片：保持原2x2布局
            canvas_width = target_size[0] * 2 + margin
            canvas_height = target_size[1] * 2 + margin
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),
                (target_size[0] + margin, 0),
                (0, target_size[1] + margin),
                (target_size[0] + margin, target_size[1] + margin)
            ]

    elif is_landscape:
        # 第一张图片宽度 1024，高度按比例
        target_width1 = 1024
        target_height1 = int(target_width1 / aspect_ratio)
        target_size1 = (target_width1, target_height1)

        # 根据有效图片数量调整第二层图片尺寸
        if successful_downloads == 2:
            # 2张图片：上1下1，第二张图片与第一张同尺寸
            target_size2 = target_size1
        else:
            # 3张或4张图片：其他图片宽度为1024除以数量
            num_bottom = successful_downloads - 1
            target_width2 = target_width1 // num_bottom
            target_height2 = int(target_width2 / aspect_ratio)
            target_size2 = (target_width2, target_height2)

        for i, path in enumerate(local_paths):
            if path and os.path.exists(path):
                img = Image.open(path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                target_size = target_size1 if i == 0 else target_size2
                img = img.resize(target_size, Image.Resampling.LANCZOS)
                pil_images.append(img)
            # 不再添加白色填充图片

        # 根据有效图片数量调整画布和位置
        if successful_downloads == 2:
            # 2张图片：上1下1
            canvas_width = target_width1
            canvas_height = target_height1 * 2 + margin
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),  # 第一张
                (0, target_height1 + margin)  # 第二张
            ]
        elif successful_downloads == 3:
            # 3张图片：上1下2
            canvas_width = target_width1
            canvas_height = target_height1 + target_size2[1] + margin
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),  # 第一张
                (0, target_height1 + margin),  # 第二张
                (target_size2[0] + margin, target_height1 + margin)  # 第三张
            ]
        else:
            # 4张图片：保持原布局
            canvas_width = target_width1
            canvas_height = target_height1 + target_size2[1] + margin
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),  # 第一张
                (0, target_height1 + margin),  # 第二张
                (target_size2[0] + margin, target_height1 + margin),  # 第三张
                (2 * target_size2[0] + 2 * margin, target_height1 + margin)  # 第四张
            ]

    else:  # is_portrait
        # 第一张图片高度 1024，宽度按比例
        target_height1 = 1024
        target_width1 = int(target_height1 * aspect_ratio)
        target_size1 = (target_width1, target_height1)

        # 根据有效图片数量调整第二列图片尺寸
        if successful_downloads == 2:
            # 2张图片：左1右1，第二张图片与第一张同尺寸
            target_size2 = target_size1
        else:
            # 3张或4张图片：其他图片高度为1024除以数量
            num_right = successful_downloads - 1
            target_height2 = target_height1 // num_right
            target_width2 = int(target_height2 * aspect_ratio)
            target_size2 = (target_width2, target_height2)

        for i, path in enumerate(local_paths):
            if path and os.path.exists(path):
                img = Image.open(path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                target_size = target_size1 if i == 0 else target_size2
                img = img.resize(target_size, Image.Resampling.LANCZOS)
                pil_images.append(img)
            # 不再添加白色填充图片

        # 根据有效图片数量调整画布和位置
        if successful_downloads == 2:
            # 2张图片：左1右1
            canvas_width = target_width1 * 2 + margin
            canvas_height = target_height1
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),  # 第一张
                (target_width1 + margin, 0)  # 第二张
            ]
        elif successful_downloads == 3:
            # 3张图片：左1右2
            canvas_width = target_width1 + target_size2[0] + margin
            canvas_height = target_height1
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),  # 第一张
                (target_width1 + margin, 0),  # 第二张
                (target_width1 + margin, target_size2[1] + margin)  # 第三张
            ]
        else:
            # 4张图片：保持原布局
            canvas_width = target_width1 + target_size2[0] + margin
            canvas_height = target_height1
            canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')
            positions = [
                (0, 0),  # 第一张
                (target_width1 + margin, 0),  # 第二张
                (target_width1 + margin, target_size2[1] + margin),  # 第三张
                (target_width1 + margin, 2 * target_size2[1] + 2 * margin)  # 第四张
            ]

    # 粘贴图片到画布
    for i, (img, pos) in enumerate(zip(pil_images, positions)):
        if img is not None:  # 确保图片对象有效
            # 直接粘贴图片，不添加额外边框（画布已经包含了边距）
            canvas.paste(img, pos)

    # 保存合并后的图片
    combined_filename = f"combined_{int(time.time())}.jpg"
    combined_path = os.path.join(storage_dir, combined_filename)
    canvas.save(combined_path, 'JPEG', quality=95)

    # 清理PIL图片对象
    for img in pil_images:
        if img is not None:
            img.close()

    # 构造返回的URL路径
    files_url = os.getenv('FILES_URL', 'http://************:54107')
    combined_url = f"{files_url}/storage/download_files/{combined_filename}"

    # 生成Markdown格式的图片URL
    markdown_pic_url = '\n'.join([f"![image]({url})" for url in pic_urls[:4] if url])

    # 返回结果，包含调试信息
    return {
        "pic_urls": pic_urls,
        "pic_url1": pic_urls[0] if len(pic_urls) > 0 else "",
        "pic_url2": pic_urls[1] if len(pic_urls) > 1 else "",
        "pic_url3": pic_urls[2] if len(pic_urls) > 2 else "",
        "pic_url4": pic_urls[3] if len(pic_urls) > 3 else "",
        "combined_image_path": combined_url,
        "markdown_pic_urls": markdown_pic_url,
        "message": message,
        "debug_info": {
            "total_urls": len(pic_urls),
            "successful_downloads": successful_downloads,
            "processed_images": len([img for img in pil_images if img is not None]),
            "aspect_ratio": aspect_ratio,
            "layout_type": f"{'square' if is_square else ('landscape' if is_landscape else 'portrait')}_{successful_downloads}pics"
        }
    }