import json
import os
import requests
from PIL import Image
from io import BytesIO
import time

def main(arg1: str) -> dict:
    # 解析输入的JSON字符串
    data = json.loads(arg1)
    pic_urls = [item['url'] for item in data['data']]

    # 处理message字段（新arg1格式兼容）
    message = ""
    if 'message' in data:
        # 去除首尾空格和换行符
        message = data['message'].strip().replace('\n', '')


    # 定义存储目录
    storage_dir = "/var/sandbox/sandbox-python/usr/local/storage/download_files"
    if not os.path.exists(storage_dir):
        os.makedirs(storage_dir)

    # 下载图片到本地并获取尺寸
    local_paths = []
    first_image_size = None
    successful_downloads = 0

    for i, url in enumerate(pic_urls[:4]):  # 最多处理4张图片
        try:
            response = requests.get(url, timeout=30)
            if response.status_code == 200:
                # 使用更精确的时间戳避免文件名冲突
                timestamp = int(time.time() * 1000000)  # 微秒级时间戳
                img_path = os.path.join(storage_dir, f"image_{i+1}_{timestamp}.jpg")

                # 确保文件名唯一
                counter = 0
                while os.path.exists(img_path):
                    counter += 1
                    img_path = os.path.join(storage_dir, f"image_{i+1}_{timestamp}_{counter}.jpg")

                with open(img_path, 'wb') as f:
                    f.write(response.content)

                # 验证图片文件是否有效
                try:
                    test_img = Image.open(img_path)
                    test_img.verify()  # 验证图片完整性
                    test_img.close()

                    local_paths.append(img_path)
                    successful_downloads += 1

                    # 获取第一张成功下载图片的尺寸
                    if first_image_size is None:
                        img = Image.open(img_path)
                        first_image_size = img.size
                        img.close()

                except Exception as img_error:
                    # 图片文件损坏，删除并标记为失败
                    if os.path.exists(img_path):
                        os.remove(img_path)
                    local_paths.append(None)
            else:
                local_paths.append(None)
        except Exception as e:
            local_paths.append(None)

    # 如果没有成功下载任何图片，返回错误
    if successful_downloads == 0:
        return {
            "error": "所有图片下载失败",
            "pic_urls": pic_urls,
            "combined_image_path": "",
            "markdown_pic_urls": "",
            "message": message
        }

    # 判断图片比例
    aspect_ratio = first_image_size[0] / first_image_size[1] if first_image_size else 1.0
    is_square = 0.67 <= aspect_ratio <= 1.5
    is_landscape = aspect_ratio > 1.5
    is_portrait = aspect_ratio < 0.67

    # 合并图片
    pil_images = []
    margin = 4  # 分割线宽度（每边）

    # 加载图片并调整大小
    if is_square:
        target_size = (512, 512)
        for path in local_paths:
            if path and os.path.exists(path):
                img = Image.open(path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                img = img.resize(target_size, Image.Resampling.LANCZOS)
                pil_images.append(img)
            else:
                pil_images.append(Image.new('RGB', target_size, 'white'))

        # 2x2 画布
        canvas_width = target_size[0] * 2 + margin
        canvas_height = target_size[1] * 2 + margin
        canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')

        # 按顺序粘贴：左上、右上、左下、右下
        positions = [
            (0, 0),
            (target_size[0] + margin, 0),
            (0, target_size[1] + margin),
            (target_size[0] + margin, target_size[1] + margin)
        ]

    elif is_landscape:
        # 第一张图片宽度 1024，高度按比例
        target_width1 = 1024
        target_height1 = int(target_width1 / aspect_ratio)
        target_size1 = (target_width1, target_height1)
        # 其他三张图片宽度 1024/3，高度按比例
        target_width2 = target_width1 // 3
        target_height2 = int(target_width2 / aspect_ratio)
        target_size2 = (target_width2, target_height2)

        for i, path in enumerate(local_paths):
            if path and os.path.exists(path):
                img = Image.open(path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                target_size = target_size1 if i == 0 else target_size2
                img = img.resize(target_size, Image.Resampling.LANCZOS)
                pil_images.append(img)
            else:
                target_size = target_size1 if i == 0 else target_size2
                pil_images.append(Image.new('RGB', target_size, 'white'))

        # 1x3 画布：上排宽 1024，下排宽 1024（3 张）
        canvas_width = target_width1
        canvas_height = target_height1 + target_height2 + margin
        canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')

        # 按顺序粘贴：上第一张，下左到右第二、三、四张
        positions = [
            (0, 0),  # 第一张
            (0, target_height1 + margin),  # 第二张
            (target_width2 + margin, target_height1 + margin),  # 第三张
            (2 * target_width2 + 2 * margin, target_height1 + margin)  # 第四张
        ]

    else:  # is_portrait
        # 第一张图片高度 1024，宽度按比例
        target_height1 = 1024
        target_width1 = int(target_height1 * aspect_ratio)
        target_size1 = (target_width1, target_height1)
        # 其他三张图片高度 1024/3，宽度按比例
        target_height2 = target_height1 // 3
        target_width2 = int(target_height2 * aspect_ratio)
        target_size2 = (target_width2, target_height2)

        for i, path in enumerate(local_paths):
            if path and os.path.exists(path):
                img = Image.open(path)
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                target_size = target_size1 if i == 0 else target_size2
                img = img.resize(target_size, Image.Resampling.LANCZOS)
                pil_images.append(img)
            else:
                target_size = target_size1 if i == 0 else target_size2
                pil_images.append(Image.new('RGB', target_size, 'white'))

        # 1x3 画布：左列高 1024，右列高 1024（3 张）
        canvas_width = target_width1 + target_width2 + margin
        canvas_height = target_height1
        canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')

        # 按顺序粘贴：左第一张，右上到下第二、三、四张
        positions = [
            (0, 0),  # 第一张
            (target_width1 + margin, 0),  # 第二张
            (target_width1 + margin, target_height2 + margin),  # 第三张
            (target_width1 + margin, 2 * target_height2 + 2 * margin)  # 第四张
        ]

    # 粘贴图片到画布
    for i, (img, pos) in enumerate(zip(pil_images, positions)):
        if img is not None:  # 确保图片对象有效
            # 直接粘贴图片，不添加额外边框（画布已经包含了边距）
            canvas.paste(img, pos)

    # 保存合并后的图片
    combined_filename = f"combined_{int(time.time())}.jpg"
    combined_path = os.path.join(storage_dir, combined_filename)
    canvas.save(combined_path, 'JPEG', quality=95)

    # 清理PIL图片对象
    for img in pil_images:
        if img is not None:
            img.close()

    # 构造返回的URL路径
    files_url = os.getenv('FILES_URL', 'http://************:54107')
    combined_url = f"{files_url}/storage/download_files/{combined_filename}"

    # 生成Markdown格式的图片URL
    markdown_pic_url = '\n'.join([f"![image]({url})" for url in pic_urls[:4] if url])

    # 返回结果，包含调试信息
    return {
        "pic_urls": pic_urls,
        "pic_url1": pic_urls[0] if len(pic_urls) > 0 else "",
        "pic_url2": pic_urls[1] if len(pic_urls) > 1 else "",
        "pic_url3": pic_urls[2] if len(pic_urls) > 2 else "",
        "pic_url4": pic_urls[3] if len(pic_urls) > 3 else "",
        "combined_image_path": combined_url,
        "markdown_pic_urls": markdown_pic_url,
        "message": message,
        "debug_info": {
            "total_urls": len(pic_urls),
            "successful_downloads": successful_downloads,
            "processed_images": len([img for img in pil_images if img is not None]),
            "aspect_ratio": aspect_ratio,
            "layout_type": "square" if is_square else ("landscape" if is_landscape else "portrait")
        }
    }