app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: vidoe
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.21@cb32d252bc1ebc61437c9134e22db2be5ccdd48223e08b9ea0deff4f0df0a187
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: agimaster/justwait:0.0.2@b91dbf4317fb40575f2631ebf8cecae7db08ee8da2284c418a79b564fa583a38
kind: app
version: 0.2.0
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions:
      - .JPG
      - .JPEG
      - .PNG
      - .GIF
      - .WEBP
      - .SVG
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - local_file
      - remote_url
      enabled: false
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 30
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        sourceType: start
        targetType: llm
      id: 1746506118934-llm
      source: '1746506118934'
      sourceHandle: source
      target: llm
      targetHandle: target
      type: custom
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: http-request
      id: llm-source-1746507062323-target
      source: llm
      sourceHandle: source
      target: '1746507062323'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: http-request
        targetType: tool
      id: 1746507062323-source-1746507145239-target
      source: '1746507062323'
      sourceHandle: source
      target: '1746507145239'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: code
      id: 1746507145239-source-1746507168818-target
      source: '1746507145239'
      sourceHandle: source
      target: '1746507168818'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 1746507168818-source-answer-target
      source: '1746507168818'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: '1746506118934'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 10
        model:
          completion_params: {}
          mode: chat
          name: qwen2.5-vl-7b-instruct
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - role: system
          text: "你是一名专业的 AI 视频生成参数构建专家，你的任务是根据用户意图，构建完整的视频生成参数，并将其组织为结构化的 JSON 格式输出，要求：\n\
            1、video_subject、video_script、video_aspect、voice_name参数需要根据用户输入内容，按照参数判定规则进行提取填写，其他参数必须保持原固定值不变！！\n\
            2、需要输出的结构化的 JSON 格式及参数判定规则：\n\n{\n  \"video_subject\": \"用户请求生成视频的主题（如：小蝌蚪找妈妈的故事/成语刻舟求剑的讲解）\"\
            ,\n  \"video_script\": \"视频文案（除非用户指定，否则留空）\",\n  \"video_terms\": \"string（固定值，后面没特意说明的参数均保持固定值）\"\
            ,\n  \"video_aspect\": \"视频比例（可选：16:9 和 9:16；优先使用用户指定横/竖屏属性：横屏：16:9；竖屏：9:16；默认：16:9）\"\
            ,\n  \"video_concat_mode\": \"random\",\n  \"video_transition_mode\":\
            \ \"None\",\n  \"video_clip_duration\": 4,\n  \"video_count\": 1,\n  \"\
            video_source\": \"pexels\",\n  \"video_materials\": [\n    {\n      \"\
            provider\": \"pexels\",\n      \"url\": \"\",\n      \"duration\": 0\n\
            \    }\n  ],\n  \"video_language\": \"\",\n  \"voice_name\": \"配音语音名称（优先使用用户指定男/女声音属性；男声：zh-CN-YunxiNeural；女声：zh-CN-XiaoxiaoNeural；默认：zh-CN-YunxiNeural）\"\
            ,\n  \"voice_volume\": 1,\n  \"voice_rate\": 1,\n  \"bgm_type\": \"random\"\
            ,\n  \"bgm_file\": \"\",\n  \"bgm_volume\": 0.2,\n  \"subtitle_enabled\"\
            : true,\n  \"subtitle_position\": \"bottom\",\n  \"custom_position\":\
            \ 70,\n  \"font_name\": \"STHeitiMedium.ttc\",\n  \"text_fore_color\"\
            : \"#FFFFFF\",\n  \"text_background_color\": true,\n  \"font_size\": 60,\n\
            \  \"stroke_color\": \"#000000\",\n  \"stroke_width\": 1.5,\n  \"n_threads\"\
            : 2,\n  \"paragraph_number\": 1\n}\n"
        selected: false
        title: 视频参数结构化
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: llm
      position:
        x: 380
        y: 282
      positionAbsolute:
        x: 380
        y: 282
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1746507168818.markdown_result#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 104
      id: answer
      position:
        x: 1580
        y: 282
      positionAbsolute:
        x: 1580
        y: 282
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - id: key-value-1653
            key: ''
            type: text
            value: '{{#llm.text#}}'
          type: json
        desc: ''
        headers: Content-Type:application/json
        method: post
        params: ''
        retry_config:
          max_retries: 3
          retry_enabled: false
          retry_interval: 100
        selected: false
        ssl_verify: true
        timeout:
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
        title: 视频生成后台服务
        type: http-request
        url: http://************:8502/api/v1/videos
        variables: []
      height: 109
      id: '1746507062323'
      position:
        x: 680
        y: 282
      positionAbsolute:
        x: 680
        y: 282
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: The number of seconds to wait
            ja_JP: The number of seconds to wait
            pt_BR: The number of seconds to wait
            zh_Hans: 等待的秒数
          label:
            en_US: Seconds
            ja_JP: Seconds
            pt_BR: Seconds
            zh_Hans: 秒数
          llm_description: The number of seconds to pause execution
          max: null
          min: null
          name: seconds
          options: []
          placeholder: null
          precision: null
          required: true
          scope: null
          template: null
          type: number
        params:
          seconds: ''
        provider_id: agimaster/justwait/justwait
        provider_name: agimaster/justwait/justwait
        provider_type: builtin
        selected: false
        title: 等待
        tool_configurations: {}
        tool_description: 等待指定的秒数，类似于Python的time.sleep()函数
        tool_label: 等待
        tool_name: wait
        tool_parameters:
          seconds:
            type: constant
            value: 360
        type: tool
      height: 53
      id: '1746507145239'
      position:
        x: 980
        y: 282
      positionAbsolute:
        x: 980
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(arg1: str) -> dict:\n    import json\n    import requests\n\
          \    import time\n    \n    try:\n        # 1. 解析初始响应获取task_id\n       \
          \ if isinstance(arg1, str):\n            parsed_arg = arg1.replace('\\\\\
          \"', '\"')\n        data = json.loads(parsed_arg)\n        task_id = data[\"\
          data\"][\"task_id\"]       \n        \n        # 构造任务状态查询 URL\n        status_url\
          \ = f\"http://************:8502/api/v1/tasks/{task_id}\"\n        headers\
          \ = {\"accept\": \"application/json\"}\n        \n        # 轮询参数\n     \
          \   max_wait_time = 240  # 最大等待时间(秒)\n        poll_interval = 10   # 轮询间隔(秒)\n\
          \        start_time = time.time()\n        \n        while time.time() -\
          \ start_time < max_wait_time:\n            try:\n                response\
          \ = requests.get(status_url, headers=headers, timeout=10)\n            \
          \    response.raise_for_status()\n                response_data = response.json()\n\
          \                \n                # 检查API响应状态\n                if response_data.get(\"\
          status\") != 200:\n                    return {\"error\": f\"API返回错误: {response_data.get('message',\
          \ '未知错误')}\"}\n                \n                task_data = response_data.get(\"\
          data\", {})\n                state = task_data.get(\"state\")\n        \
          \        \n                # 状态处理逻辑\n                if state == 1:  # 任务完成\n\
          \                    videos = task_data.get(\"videos\", [])\n          \
          \          if not videos:\n                        return {\"error\": \"\
          任务完成但无视频URL\"}\n                    \n                    video_url = videos[0]\n\
          \                    markdown_result = f'<video controls><source src=\"\
          {video_url}\" type=\"video/mp4\"></video>'\n                    return {\n\
          \                        \"video_url\": video_url,\n                   \
          \     \"markdown_result\": markdown_result\n                    }\n    \
          \            \n                elif state == 4:  # 任务进行中\n             \
          \       time.sleep(poll_interval)\n                    continue\n      \
          \          \n                else:\n                    return {\"error\"\
          : f\"未知任务状态: {state}\"}\n            \n            except requests.RequestException\
          \ as e:\n                return {\"error\": f\"任务状态查询请求失败: {str(e)}\"}\n\
          \            except json.JSONDecodeError:\n                return {\"error\"\
          : \"任务状态响应解析失败: 无效的JSON格式\"}\n        \n        return {\"error\": \"任务未在规定时间内完成\"\
          }\n    \n    except KeyError as e:\n        return {\"error\": f\"缺少必要字段:\
          \ {str(e)}\"}\n    except json.JSONDecodeError as e:\n        return {\"\
          error\": f\"输入解析失败: {str(e)}\"}\n    except Exception as e:\n        return\
          \ {\"error\": f\"系统错误: {str(e)}\"}"
        code_language: python3
        desc: ''
        outputs:
          markdown_result:
            children: null
            type: string
          video_url:
            children: null
            type: string
        selected: false
        title: 视频url处理
        type: code
        variables:
        - value_selector:
          - '1746507062323'
          - body
          variable: arg1
      height: 53
      id: '1746507168818'
      position:
        x: 1280
        y: 282
      positionAbsolute:
        x: 1280
        y: 282
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -68.14095052252412
      y: 15.767310952104367
      zoom: 0.6597539553864471
