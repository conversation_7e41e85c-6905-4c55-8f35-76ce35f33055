app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: jimeng
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/tongyi:0.0.25@325423749d5e71f8b4681af1b2ed46d7686cb1947e443a86ba5d14ac45ff85a4
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.16@77274df8fe2632cac66bfd153fcc75aa5e96abbe92b5c611b8984ad9f4cd4457
kind: app
version: 0.3.0
workflow:
  conversation_variables:
  - description: 多图修图提示词数组
    id: 9cc4b9a5-ed00-4ae4-b51d-1a16665a8bd0
    name: pics_prompts
    selector:
    - conversation
    - pics_prompts
    value: []
    value_type: array[string]
  - description: 多图修图第一张图片
    id: cf37b845-4994-4425-9d73-2a3a9911b09c
    name: pic1
    selector:
    - conversation
    - pic1
    value: ''
    value_type: string
  - description: 图片url数组
    id: f108080b-5691-498c-86b0-6e72cc4f6760
    name: pic_url
    selector:
    - conversation
    - pic_url
    value: []
    value_type: array[string]
  environment_variables:
  - description: ''
    id: 6b8b66a3-4d32-4654-93ff-c3f4d2ff7518
    name: doubao_apikey
    selector:
    - env
    - doubao_apikey
    value: sk-1234
    value_type: string
  - description: ''
    id: 27392704-d281-4a5f-8d3b-796d92f7d11c
    name: jimeng_apikey
    selector:
    - env
    - jimeng_apikey
    value: ''
    value_type: secret
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 30
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 3
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: if-else
      id: 1747052157185-source-1747109534984-target
      selected: false
      source: '1747052157185'
      sourceHandle: source
      target: '1747109534984'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: list-operator
      id: 1747109534984-true-1747119436299-target
      selected: false
      source: '1747109534984'
      sourceHandle: 'true'
      target: '1747119436299'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: if-else
      id: 1747109534984-false-1747126467545-target
      selected: false
      source: '1747109534984'
      sourceHandle: 'false'
      target: '1747126467545'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: http-request
        targetType: code
      id: 17471327230020-source-17471369319000-target
      selected: false
      source: '17471327230020'
      sourceHandle: source
      target: '17471369319000'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: if-else
      id: 1747055122415-source-1747142489643-target
      selected: false
      source: '1747055122415'
      sourceHandle: source
      target: '1747142489643'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 1747142489643-false-answer-target
      selected: false
      source: '1747142489643'
      sourceHandle: 'false'
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 1747142489643-true-1747142591712-target
      selected: false
      source: '1747142489643'
      sourceHandle: 'true'
      target: '1747142591712'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: assigner
      id: 1747055122415-source-17471427081180-target
      selected: false
      source: '1747055122415'
      sourceHandle: source
      target: '17471427081180'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: assigner
      id: 17471369319000-source-17471427431470-target
      selected: false
      source: '17471369319000'
      sourceHandle: source
      target: '17471427431470'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: if-else
      id: 17471369319000-source-17471428550290-target
      selected: false
      source: '17471369319000'
      sourceHandle: source
      target: '17471428550290'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 17471428550290-false-17471428735390-target
      selected: false
      source: '17471428550290'
      sourceHandle: 'false'
      target: '17471428735390'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: answer
      id: 17471428550290-true-17471428936900-target
      selected: false
      source: '17471428550290'
      sourceHandle: 'true'
      target: '17471428936900'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: variable-aggregator
      id: 1747157225083-source-1747119741130-target
      selected: false
      source: '1747157225083'
      sourceHandle: source
      target: '1747119741130'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: code
      id: 1747109534984-ed50b89f-c542-4734-be72-65f17ca62b2b-1747157225083-target
      selected: false
      source: '1747109534984'
      sourceHandle: ed50b89f-c542-4734-be72-65f17ca62b2b
      target: '1747157225083'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: list-operator
        targetType: code
      id: 1747119436299-source-1747156806866-target
      selected: false
      source: '1747119436299'
      sourceHandle: source
      target: '1747156806866'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: variable-aggregator
      id: 1747156806866-source-1747119741130-target
      selected: false
      source: '1747156806866'
      sourceHandle: source
      target: '1747119741130'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: question-classifier
      id: 1747126467545-true-17473687599390-target
      selected: false
      source: '1747126467545'
      sourceHandle: 'true'
      target: '17473687599390'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: code
      id: 17473687599390-2-17473688504210-target
      selected: false
      source: '17473687599390'
      sourceHandle: '2'
      target: '17473688504210'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: code
      id: 17473688504210-source-17473690296460-target
      selected: false
      source: '17473688504210'
      sourceHandle: source
      target: '17473690296460'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: variable-aggregator
      id: 17473690296460-source-1747119741130-target
      selected: false
      source: '17473690296460'
      sourceHandle: source
      target: '1747119741130'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 17473689783710-source-17473690032030-target
      selected: false
      source: '17473689783710'
      sourceHandle: source
      target: '17473690032030'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: code
      id: 17473687599390-1-17473689783710-target
      selected: false
      source: '17473687599390'
      sourceHandle: '1'
      target: '17473689783710'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: variable-aggregator
        targetType: question-classifier
      id: 1747119741130-source-17473943558420-target
      selected: false
      source: '1747119741130'
      sourceHandle: source
      target: '17473943558420'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: llm
        targetType: http-request
      id: 1748234699992-source-17482321830520-target
      selected: false
      source: '1748234699992'
      sourceHandle: source
      target: '17482321830520'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: http-request
        targetType: variable-aggregator
      id: 1747052169396-source-1748253304926-target
      selected: false
      source: '1747052169396'
      sourceHandle: source
      target: '1748253304926'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: variable-aggregator
        targetType: code
      id: 1748253304926-source-1747055122415-target
      selected: false
      source: '1748253304926'
      sourceHandle: source
      target: '1747055122415'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: http-request
        targetType: variable-aggregator
      id: 17482321830520-source-1748253304926-target
      selected: false
      source: '17482321830520'
      sourceHandle: source
      target: '1748253304926'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: parameter-extractor
        targetType: http-request
      id: 1748253720316-source-1747052169396-target
      selected: false
      source: '1748253720316'
      sourceHandle: source
      target: '1747052169396'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: llm
      id: 1748259643500-source-1748234699992-target
      selected: false
      source: '1748259643500'
      sourceHandle: source
      target: '1748234699992'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: if-else
      id: 1747126467545-false-1748264121958-target
      selected: false
      source: '1747126467545'
      sourceHandle: 'false'
      target: '1748264121958'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: if-else
      id: 17473687599390-1747368070225-1748264121958-target
      selected: false
      source: '17473687599390'
      sourceHandle: '1747368070225'
      target: '1748264121958'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: template-transform
      id: 1748264121958-false-1748259643500-target
      selected: false
      source: '1748264121958'
      sourceHandle: 'false'
      target: '1748259643500'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1748264121958-true-1748265333227-target
      selected: false
      source: '1748264121958'
      sourceHandle: 'true'
      target: '1748265333227'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: code
      id: 1748265333227-source-1748265816085-target
      selected: false
      source: '1748265333227'
      sourceHandle: source
      target: '1748265816085'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: tool
      id: 1748265816085-source-1748266385503-target
      selected: false
      source: '1748265816085'
      sourceHandle: source
      target: '1748266385503'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: code
      id: 1748266385503-source-1748267330350-target
      selected: false
      source: '1748266385503'
      sourceHandle: source
      target: '1748267330350'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 1748267330350-source-1748267532398-target
      selected: false
      source: '1748267330350'
      sourceHandle: source
      target: '1748267532398'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: template-transform
      id: 1748264121958-d06199e5-1b54-4936-9e87-31f8b53a8db6-17482676623780-target
      selected: false
      source: '1748264121958'
      sourceHandle: d06199e5-1b54-4936-9e87-31f8b53a8db6
      target: '17482676623780'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: template-transform
        targetType: llm
      id: 17482676623780-source-1748268247017-target
      selected: false
      source: '17482676623780'
      sourceHandle: source
      target: '1748268247017'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1748268247017-source-1748268435484-target
      selected: false
      source: '1748268247017'
      sourceHandle: source
      target: '1748268435484'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: tool
        targetType: code
      id: 1748268435484-source-1748268532210-target
      selected: false
      source: '1748268435484'
      sourceHandle: source
      target: '1748268532210'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: code
        targetType: answer
      id: 1748268532210-source-1748268525877-target
      selected: false
      source: '1748268532210'
      sourceHandle: source
      target: '1748268525877'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: answer
        targetType: variable-aggregator
      id: 1748267532398-source-1748268791133-target
      selected: false
      source: '1748267532398'
      sourceHandle: source
      target: '1748268791133'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: variable-aggregator
        targetType: assigner
      id: 1748268791133-source-1748268727385-target
      selected: false
      source: '1748268791133'
      sourceHandle: source
      target: '1748268727385'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: answer
        targetType: variable-aggregator
      id: 1748268525877-source-1748268791133-target
      selected: false
      source: '1748268525877'
      sourceHandle: source
      target: '1748268791133'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: template-transform
        targetType: parameter-extractor
      id: 1748260255939-source-1748253720316-target
      selected: false
      source: '1748260255939'
      sourceHandle: source
      target: '1748253720316'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: template-transform
      id: 1748264121958-3c4a809a-9f75-49ea-896a-1eba690163f2-1748260255939-target
      selected: false
      source: '1748264121958'
      sourceHandle: 3c4a809a-9f75-49ea-896a-1eba690163f2
      target: '1748260255939'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: assigner
        targetType: answer
      id: 1748432208560-source-1748434384561-target
      selected: false
      source: '1748432208560'
      sourceHandle: source
      target: '1748434384561'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: tool
      id: 1748430212628-source-1748437483795-target
      selected: false
      source: '1748430212628'
      sourceHandle: source
      target: '1748437483795'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: parameter-extractor
        targetType: http-request
      id: 17473943711060-source-17471327230020-target
      selected: false
      source: '17473943711060'
      sourceHandle: source
      target: '17471327230020'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: parameter-extractor
      id: 17473943558420-1748270344610-17473943711060-target
      selected: false
      source: '17473943558420'
      sourceHandle: '1748270344610'
      target: '17473943711060'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: assigner
        targetType: answer
      id: 1748441783383-source-1748437679409-target
      selected: false
      source: '1748441783383'
      sourceHandle: source
      target: '1748437679409'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: question-classifier
        targetType: if-else
      id: 17473943558420-1748270263201-1748431592747-target
      selected: false
      source: '17473943558420'
      sourceHandle: '1748270263201'
      target: '1748431592747'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: assigner
      id: 1748431592747-true-1748432208560-target
      selected: false
      source: '1748431592747'
      sourceHandle: 'true'
      target: '1748432208560'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: if-else
        targetType: llm
      id: 1748431592747-false-1748430212628-target
      selected: false
      source: '1748431592747'
      sourceHandle: 'false'
      target: '1748430212628'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: tool
        targetType: assigner
      id: 1748437483795-source-1748441783383-target
      selected: false
      source: '1748437483795'
      sourceHandle: source
      target: '1748441783383'
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: '1747052157185'
      position:
        x: 30
        y: 337.5
      positionAbsolute:
        x: 30
        y: 337.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - id: key-value-249
            key: ''
            type: text
            value: "{\n  \"model\": \"jimeng-3.0\",\n  \"prompt\": \"{{#1748253720316.prompt#}}\"\
              ,\n  \"negative_prompt\": \"{{#1748253720316.negative_prompt#}}\",\n\
              \  \"width\": {{#1748253720316.width#}},\n  \"height\": {{#1748253720316.height#}},\n\
              \  \"sample_strength\": 0.5,\n  \"response_format\": \"url\"\n}"
          type: json
        desc: ''
        headers: 'Authorization:Bearer {{#env.jimeng_apikey#}}

          Content-Type:application/json'
        method: post
        params: ''
        retry_config:
          max_retries: 1
          retry_enabled: false
          retry_interval: '1000'
        selected: false
        ssl_verify: true
        timeout:
          connect: 180
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
          read: 180
          write: 180
        title: JM文生图
        type: http-request
        url: http://************:8061/v1/images/generations
        variables: []
      height: 109
      id: '1747052169396'
      position:
        x: 2551.0089132182616
        y: 911.7196386133427
      positionAbsolute:
        x: 2551.0089132182616
        y: 911.7196386133427
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '![image]({{#1747055122415.combined_image_path#}})'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 120
      id: answer
      position:
        x: 4202.704170603376
        y: 782.3553065006397
      positionAbsolute:
        x: 4202.704170603376
        y: 782.3553065006397
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\nimport os\nimport requests\nfrom PIL import Image\nfrom\
          \ io import BytesIO\nimport time\n\ndef main(arg1: str) -> dict:\n    #\
          \ 解析输入的JSON字符串\n    data = json.loads(arg1)\n    pic_urls = [item['url']\
          \ for item in data['data']]\n\n    # 处理message字段（新arg1格式兼容）\n    message\
          \ = \"\"\n    if 'message' in data:\n        # 去除首尾空格和换行符\n        message\
          \ = data['message'].strip().replace('\\n', '')\n\n\n    # 定义存储目录\n    storage_dir\
          \ = \"/var/sandbox/sandbox-python/usr/local/storage/download_files\"\n \
          \   if not os.path.exists(storage_dir):\n        os.makedirs(storage_dir)\n\
          \n    # 下载图片到本地并获取尺寸\n    local_paths = []\n    first_image_size = None\n\
          \    for i, url in enumerate(pic_urls[:4]):  # 最多处理4张图片\n        try:\n\
          \            response = requests.get(url, timeout=30)\n            if response.status_code\
          \ == 200:\n                img_path = os.path.join(storage_dir, f\"image_{i+1}_{int(time.time())}.jpg\"\
          )\n                with open(img_path, 'wb') as f:\n                   \
          \ f.write(response.content)\n                local_paths.append(img_path)\n\
          \                # 获取第一张图片的尺寸\n                if i == 0:\n            \
          \        img = Image.open(img_path)\n                    first_image_size\
          \ = img.size\n                    img.close()\n            else:\n     \
          \           local_paths.append(None)\n        except Exception as e:\n \
          \           local_paths.append(None)\n\n    # 判断图片比例\n    aspect_ratio =\
          \ first_image_size[0] / first_image_size[1] if first_image_size else 1.0\n\
          \    is_square = 0.67 <= aspect_ratio <= 1.5\n    is_landscape = aspect_ratio\
          \ > 1.5\n    is_portrait = aspect_ratio < 0.67\n\n    # 合并图片\n    pil_images\
          \ = []\n    margin = 4  # 分割线宽度（每边）\n\n    # 加载图片并调整大小\n    if is_square:\n\
          \        target_size = (512, 512)\n        for path in local_paths:\n  \
          \          if path and os.path.exists(path):\n                img = Image.open(path)\n\
          \                if img.mode != 'RGB':\n                    img = img.convert('RGB')\n\
          \                img = img.resize(target_size, Image.Resampling.LANCZOS)\n\
          \                pil_images.append(img)\n            else:\n           \
          \     pil_images.append(Image.new('RGB', target_size, 'white'))\n\n    \
          \    # 2x2 画布\n        canvas_width = target_size[0] * 2 + margin\n    \
          \    canvas_height = target_size[1] * 2 + margin\n        canvas = Image.new('RGB',\
          \ (canvas_width, canvas_height), 'white')\n\n        # 按顺序粘贴：左上、右上、左下、右下\n\
          \        positions = [\n            (0, 0),\n            (target_size[0]\
          \ + margin, 0),\n            (0, target_size[1] + margin),\n           \
          \ (target_size[0] + margin, target_size[1] + margin)\n        ]\n\n    elif\
          \ is_landscape:\n        # 第一张图片宽度 1024，高度按比例\n        target_width1 = 1024\n\
          \        target_height1 = int(target_width1 / aspect_ratio)\n        target_size1\
          \ = (target_width1, target_height1)\n        # 其他三张图片宽度 1024/3，高度按比例\n \
          \       target_width2 = target_width1 // 3\n        target_height2 = int(target_width2\
          \ / aspect_ratio)\n        target_size2 = (target_width2, target_height2)\n\
          \n        for i, path in enumerate(local_paths):\n            if path and\
          \ os.path.exists(path):\n                img = Image.open(path)\n      \
          \          if img.mode != 'RGB':\n                    img = img.convert('RGB')\n\
          \                target_size = target_size1 if i == 0 else target_size2\n\
          \                img = img.resize(target_size, Image.Resampling.LANCZOS)\n\
          \                pil_images.append(img)\n            else:\n           \
          \     target_size = target_size1 if i == 0 else target_size2\n         \
          \       pil_images.append(Image.new('RGB', target_size, 'white'))\n\n  \
          \      # 1x3 画布：上排宽 1024，下排宽 1024（3 张）\n        canvas_width = target_width1\n\
          \        canvas_height = target_height1 + target_height2 + margin\n    \
          \    canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')\n\
          \n        # 按顺序粘贴：上第一张，下左到右第二、三、四张\n        positions = [\n            (0,\
          \ 0),  # 第一张\n            (0, target_height1 + margin),  # 第二张\n       \
          \     (target_width2 + margin, target_height1 + margin),  # 第三张\n      \
          \      (2 * target_width2 + 2 * margin, target_height1 + margin)  # 第四张\n\
          \        ]\n\n    else:  # is_portrait\n        # 第一张图片高度 1024，宽度按比例\n \
          \       target_height1 = 1024\n        target_width1 = int(target_height1\
          \ * aspect_ratio)\n        target_size1 = (target_width1, target_height1)\n\
          \        # 其他三张图片高度 1024/3，宽度按比例\n        target_height2 = target_height1\
          \ // 3\n        target_width2 = int(target_height2 * aspect_ratio)\n   \
          \     target_size2 = (target_width2, target_height2)\n\n        for i, path\
          \ in enumerate(local_paths):\n            if path and os.path.exists(path):\n\
          \                img = Image.open(path)\n                if img.mode !=\
          \ 'RGB':\n                    img = img.convert('RGB')\n               \
          \ target_size = target_size1 if i == 0 else target_size2\n             \
          \   img = img.resize(target_size, Image.Resampling.LANCZOS)\n          \
          \      pil_images.append(img)\n            else:\n                target_size\
          \ = target_size1 if i == 0 else target_size2\n                pil_images.append(Image.new('RGB',\
          \ target_size, 'white'))\n\n        # 1x3 画布：左列高 1024，右列高 1024（3 张）\n  \
          \      canvas_width = target_width1 + target_width2 + margin\n        canvas_height\
          \ = target_height1\n        canvas = Image.new('RGB', (canvas_width, canvas_height),\
          \ 'white')\n\n        # 按顺序粘贴：左第一张，右上到下第二、三、四张\n        positions = [\n\
          \            (0, 0),  # 第一张\n            (target_width1 + margin, 0),  #\
          \ 第二张\n            (target_width1 + margin, target_height2 + margin),  #\
          \ 第三张\n            (target_width1 + margin, 2 * target_height2 + 2 * margin)\
          \  # 第四张\n        ]\n\n    # 粘贴图片到画布\n    for img, pos in zip(pil_images,\
          \ positions):\n        # 直接粘贴图片，不添加额外边框（画布已经包含了边距）\n        canvas.paste(img,\
          \ pos)\n\n    # 保存合并后的图片\n    combined_filename = f\"combined_{int(time.time())}.jpg\"\
          \n    combined_path = os.path.join(storage_dir, combined_filename)\n   \
          \ canvas.save(combined_path, 'JPEG', quality=95)\n\n    # 清理PIL图片对象\n  \
          \  for img in pil_images:\n        img.close()\n\n    # 构造返回的URL路径\n   \
          \ files_url = os.getenv('FILES_URL', 'http://************:54107')\n    combined_url\
          \ = f\"{files_url}/storage/download_files/{combined_filename}\"\n\n    #\
          \ 生成Markdown格式的图片URL\n    markdown_pic_url = '\\n'.join([f\"![image]({url})\"\
          \ for url in pic_urls[:4] if url])\n\n    # 返回结果\n    return {\n       \
          \ \"pic_url\": pic_urls,\n        \"pic_url1\": pic_urls[0] if len(pic_urls)\
          \ > 0 else \"\",\n        \"pic_url2\": pic_urls[1] if len(pic_urls) > 1\
          \ else \"\",\n        \"pic_url3\": pic_urls[2] if len(pic_urls) > 2 else\
          \ \"\",\n        \"pic_url4\": pic_urls[3] if len(pic_urls) > 3 else \"\"\
          ,\n        \"combined_image_path\": combined_url,\n        \"markdown_pic_url\"\
          : markdown_pic_url,\n        \"message\": message\n    }"
        code_language: python3
        desc: ''
        outputs:
          combined_image_path:
            children: null
            type: string
          markdown_pic_url:
            children: null
            type: string
          message:
            children: null
            type: string
          pic_url:
            children: null
            type: array[string]
          pic_url1:
            children: null
            type: string
          pic_url2:
            children: null
            type: string
          pic_url3:
            children: null
            type: string
          pic_url4:
            children: null
            type: string
        selected: false
        title: 绘图返回数据处理
        type: code
        variables:
        - value_selector:
          - '1748253304926'
          - output
          variable: arg1
      height: 53
      id: '1747055122415'
      position:
        x: 3285.392136172053
        y: 962.1314411963529
      positionAbsolute:
        x: 3285.392136172053
        y: 962.1314411963529
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: f738bb87-3776-4279-965f-b8f446439cee
            sub_variable_condition:
              case_id: 84e25620-95a7-42cc-94b9-0880edd733c5
              conditions:
              - comparison_operator: in
                id: 0b7003fa-caf4-49a0-81b2-111a14a2cdf9
                key: type
                value:
                - image
                varType: string
              logical_operator: and
            value: ''
            varType: array[file]
            variable_selector:
            - sys
            - files
          id: 'true'
          logical_operator: and
        - case_id: ed50b89f-c542-4734-be72-65f17ca62b2b
          conditions:
          - comparison_operator: not contains
            id: a1b169a7-8783-438a-b984-beb39e520f3c
            sub_variable_condition:
              case_id: 710645af-7a6f-414f-8988-f9d61dc360c1
              conditions:
              - comparison_operator: in
                id: defb301a-8032-490d-9b9b-6a89a386b785
                key: type
                value:
                - image
                varType: string
              logical_operator: and
            value: ''
            varType: array[file]
            variable_selector:
            - sys
            - files
          - comparison_operator: contains
            id: 08e6d99c-e747-40f4-85fd-d2dfea1a5e0e
            value: http
            varType: string
            variable_selector:
            - sys
            - query
          id: ed50b89f-c542-4734-be72-65f17ca62b2b
          logical_operator: and
        desc: 暂不处理多图场景，链接为高优先级
        selected: false
        title: 是否有图片文件
        type: if-else
      height: 275
      id: '1747109534984'
      position:
        x: 334
        y: 337.5
      positionAbsolute:
        x: 334
        y: 337.5
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        extract_by:
          enabled: true
          serial: '1'
        filter_by:
          conditions:
          - comparison_operator: start with
            key: url
            value: /app/api/storage
          enabled: true
        item_var_type: file
        limit:
          enabled: false
          size: 10
        order_by:
          enabled: false
          key: ''
          value: asc
        selected: false
        title: 图片本地路径提取
        type: list-operator
        var_type: array[file]
        variable:
        - sys
        - files
      height: 91
      id: '1747119436299'
      position:
        x: 1123.9707174035545
        y: 199.8612055852418
      positionAbsolute:
        x: 1123.9707174035545
        y: 199.8612055852418
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        advanced_settings:
          group_enabled: true
          groups:
          - groupId: 49e48f9e-4dbe-429c-9645-2ef0b8bfa1f3
            group_name: pic_url
            output_type: string
            variables:
            - - '1747157225083'
              - pic_url
            - - '1747156806866'
              - file_url
            - - '17473688504210'
              - selected_url
          - groupId: 7ec30c0a-1806-476b-bb9b-1e4846cc2bb7
            group_name: prompt
            output_type: string
            variables:
            - - '1747156806866'
              - result
            - - '1747157225083'
              - prompt
            - - '17473690296460'
              - result
        desc: ''
        output_type: string
        selected: false
        title: 图片url聚合
        type: variable-aggregator
        variables:
        - - '1747109649470'
          - pic_url
        - - '1747119526527'
          - file_url
        - - '17471345275980'
          - selected_url
      height: 249
      id: '1747119741130'
      position:
        x: 1838.0452932918738
        y: 267.0503911980808
      positionAbsolute:
        x: 1838.0452932918738
        y: 267.0503911980808
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: not empty
            id: 5f7a5a8d-42ae-4541-81b2-67e22a4fc438
            value: ''
            varType: array[string]
            variable_selector:
            - conversation
            - pic_url
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 是否有参考图
        type: if-else
      height: 125
      id: '1747126467545'
      position:
        x: 559.777050544241
        y: 655.7246822040537
      positionAbsolute:
        x: 559.777050544241
        y: 655.7246822040537
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - id: key-value-249
            key: ''
            type: text
            value: "{\n\t\"image\": \"{{#1747119741130.pic_url.output#}}\",\n\t\"\
              prompt\": \"{{#1747119741130.prompt.output#}}\",\n\t\"negative_prompt\"\
              : \"模糊，低质量\",\n    \"width\": {{#1747159731406.width#}},\n    \"height\"\
              : {{#1747159731406.height#}},\n\t\"sample_strength\": 0.4,\n\t\"reference_mode\"\
              : \"{{#1747159731406.reference_mode#}}\",\n\t\"reference_strength\"\
              : 0.6,\n\t\"response_format\": \"url\"\n}"
          type: json
        desc: ''
        headers: 'Authorization:Bearer {{#env.jimeng_apikey#}}

          Content-Type:application/json'
        method: post
        params: ''
        retry_config:
          max_retries: 1
          retry_enabled: false
          retry_interval: '1000'
        selected: false
        ssl_verify: true
        timeout:
          connect: 240
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
          read: 240
          write: 240
        title: 即梦图生图
        type: http-request
        url: http://************:8061/v1/images/variations
        variables: []
      height: 109
      id: '17471327230020'
      position:
        x: 2812.264384661443
        y: 256.75768803916446
      positionAbsolute:
        x: 2812.264384661443
        y: 256.75768803916446
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\r\nimport os\r\nimport requests\r\nfrom PIL import Image\r\
          \nfrom io import BytesIO\r\nimport time\r\n\r\ndef main(arg1: str) -> dict:\r\
          \n    # 解析输入的JSON字符串\r\n    data = json.loads(arg1)\r\n    pic_urls = [item['url']\
          \ for item in data['data']]\r\n    \r\n    # 定义存储目录\r\n    storage_dir =\
          \ \"/var/sandbox/sandbox-python/usr/local/storage/download_files\"\r\n \
          \   if not os.path.exists(storage_dir):\r\n        os.makedirs(storage_dir)\r\
          \n    \r\n    # 下载图片到本地并获取尺寸\r\n    local_paths = []\r\n    first_image_size\
          \ = None\r\n    for i, url in enumerate(pic_urls[:4]):  # 最多处理4张图片\r\n \
          \       try:\r\n            response = requests.get(url, timeout=30)\r\n\
          \            if response.status_code == 200:\r\n                img_path\
          \ = os.path.join(storage_dir, f\"image_{i+1}_{int(time.time())}.jpg\")\r\
          \n                with open(img_path, 'wb') as f:\r\n                  \
          \  f.write(response.content)\r\n                local_paths.append(img_path)\r\
          \n                # 获取第一张图片的尺寸\r\n                if i == 0:\r\n       \
          \             img = Image.open(img_path)\r\n                    first_image_size\
          \ = img.size\r\n                    img.close()\r\n            else:\r\n\
          \                local_paths.append(None)\r\n        except Exception as\
          \ e:\r\n            local_paths.append(None)\r\n    \r\n    # 判断图片比例\r\n\
          \    aspect_ratio = first_image_size[0] / first_image_size[1] if first_image_size\
          \ else 1.0\r\n    is_square = 0.67 <= aspect_ratio <= 1.5\r\n    is_landscape\
          \ = aspect_ratio > 1.5\r\n    is_portrait = aspect_ratio < 0.67\r\n    \r\
          \n    # 合并图片\r\n    pil_images = []\r\n    margin = 4  # 分割线宽度（每边）\r\n \
          \   \r\n    # 加载图片并调整大小\r\n    if is_square:\r\n        target_size = (512,\
          \ 512)\r\n        for path in local_paths:\r\n            if path and os.path.exists(path):\r\
          \n                img = Image.open(path)\r\n                if img.mode\
          \ != 'RGB':\r\n                    img = img.convert('RGB')\r\n        \
          \        img = img.resize(target_size, Image.Resampling.LANCZOS)\r\n   \
          \             pil_images.append(img)\r\n            else:\r\n          \
          \      pil_images.append(Image.new('RGB', target_size, 'white'))\r\n   \
          \     \r\n        # 2x2 画布\r\n        canvas_width = (target_size[0] + 2\
          \ * margin) * 2\r\n        canvas_height = (target_size[1] + 2 * margin)\
          \ * 2\r\n        canvas = Image.new('RGB', (canvas_width, canvas_height),\
          \ 'white')\r\n        \r\n        # 按顺序粘贴：左上、右上、左下、右下\r\n        positions\
          \ = [\r\n            (margin, margin),\r\n            (target_size[0] +\
          \ 3 * margin, margin),\r\n            (margin, target_size[1] + 3 * margin),\r\
          \n            (target_size[0] + 3 * margin, target_size[1] + 3 * margin)\r\
          \n        ]\r\n    \r\n    elif is_landscape:\r\n        # 第一张图片宽度 1024，高度按比例\r\
          \n        target_width1 = 1024\r\n        target_height1 = int(target_width1\
          \ / aspect_ratio)\r\n        target_size1 = (target_width1, target_height1)\r\
          \n        # 其他三张图片宽度 1024/3，高度按比例\r\n        target_width2 = target_width1\
          \ // 3\r\n        target_height2 = int(target_width2 / aspect_ratio)\r\n\
          \        target_size2 = (target_width2, target_height2)\r\n        \r\n\
          \        for i, path in enumerate(local_paths):\r\n            if path and\
          \ os.path.exists(path):\r\n                img = Image.open(path)\r\n  \
          \              if img.mode != 'RGB':\r\n                    img = img.convert('RGB')\r\
          \n                target_size = target_size1 if i == 0 else target_size2\r\
          \n                img = img.resize(target_size, Image.Resampling.LANCZOS)\r\
          \n                pil_images.append(img)\r\n            else:\r\n      \
          \          target_size = target_size1 if i == 0 else target_size2\r\n  \
          \              pil_images.append(Image.new('RGB', target_size, 'white'))\r\
          \n        \r\n        # 1x3 画布：上排宽 1024，下排宽 1024（3 张）\r\n        canvas_width\
          \ = target_width1 + 2 * margin\r\n        canvas_height = target_height1\
          \ + target_height2 + 4 * margin\r\n        canvas = Image.new('RGB', (canvas_width,\
          \ canvas_height), 'white')\r\n        \r\n        # 按顺序粘贴：上第一张，下左到右第二、三、四张\r\
          \n        positions = [\r\n            (margin, margin),  # 第一张\r\n    \
          \        (margin, target_height1 + 3 * margin),  # 第二张\r\n            (target_width2\
          \ + 3 * margin, target_height1 + 3 * margin),  # 第三张\r\n            (2 *\
          \ target_width2 + 5 * margin, target_height1 + 3 * margin)  # 第四张\r\n  \
          \      ]\r\n    \r\n    else:  # is_portrait\r\n        # 第一张图片高度 1024，宽度按比例\r\
          \n        target_height1 = 1024\r\n        target_width1 = int(target_height1\
          \ * aspect_ratio)\r\n        target_size1 = (target_width1, target_height1)\r\
          \n        # 其他三张图片高度 1024/3，宽度按比例\r\n        target_height2 = target_height1\
          \ // 3\r\n        target_width2 = int(target_height2 * aspect_ratio)\r\n\
          \        target_size2 = (target_width2, target_height2)\r\n        \r\n\
          \        for i, path in enumerate(local_paths):\r\n            if path and\
          \ os.path.exists(path):\r\n                img = Image.open(path)\r\n  \
          \              if img.mode != 'RGB':\r\n                    img = img.convert('RGB')\r\
          \n                target_size = target_size1 if i == 0 else target_size2\r\
          \n                img = img.resize(target_size, Image.Resampling.LANCZOS)\r\
          \n                pil_images.append(img)\r\n            else:\r\n      \
          \          target_size = target_size1 if i == 0 else target_size2\r\n  \
          \              pil_images.append(Image.new('RGB', target_size, 'white'))\r\
          \n        \r\n        # 1x3 画布：左列高 1024，右列高 1024（3 张）\r\n        canvas_width\
          \ = target_width1 + target_width2 + 4 * margin\r\n        canvas_height\
          \ = target_height1 + 2 * margin\r\n        canvas = Image.new('RGB', (canvas_width,\
          \ canvas_height), 'white')\r\n        \r\n        # 按顺序粘贴：左第一张，右上到下第二、三、四张\r\
          \n        positions = [\r\n            (margin, margin),  # 第一张\r\n    \
          \        (target_width1 + 3 * margin, margin),  # 第二张\r\n            (target_width1\
          \ + 3 * margin, target_height2 + 3 * margin),  # 第三张\r\n            (target_width1\
          \ + 3 * margin, 2 * target_height2 + 5 * margin)  # 第四张\r\n        ]\r\n\
          \    \r\n    # 粘贴图片到画布\r\n    for img, pos in zip(pil_images, positions):\r\
          \n        # 为每张图片添加白色边框\r\n        img_size = img.size\r\n        bordered_img\
          \ = Image.new('RGB', (img_size[0] + 2 * margin, img_size[1] + 2 * margin),\
          \ 'white')\r\n        bordered_img.paste(img, (margin, margin))\r\n    \
          \    canvas.paste(bordered_img, pos)\r\n    \r\n    # 保存合并后的图片\r\n    combined_filename\
          \ = f\"combined_{int(time.time())}.jpg\"\r\n    combined_path = os.path.join(storage_dir,\
          \ combined_filename)\r\n    canvas.save(combined_path, 'JPEG', quality=95)\r\
          \n    \r\n    # 清理PIL图片对象\r\n    for img in pil_images:\r\n        img.close()\r\
          \n    \r\n    # 构造返回的URL路径\r\n    files_url = os.getenv('FILES_URL', 'http://************:54107')\r\
          \n    combined_url = f\"{files_url}/storage/download_files/{combined_filename}\"\
          \r\n    \r\n    # 生成Markdown格式的图片URL\r\n    markdown_pic_url = '\\n'.join([f\"\
          ![image]({url})\" for url in pic_urls[:4] if url])\r\n    \r\n    # 返回结果\r\
          \n    return {\r\n        \"pic_url\": pic_urls,\r\n        \"pic_url1\"\
          : pic_urls[0] if len(pic_urls) > 0 else \"\",\r\n        \"pic_url2\": pic_urls[1]\
          \ if len(pic_urls) > 1 else \"\",\r\n        \"pic_url3\": pic_urls[2] if\
          \ len(pic_urls) > 2 else \"\",\r\n        \"pic_url4\": pic_urls[3] if len(pic_urls)\
          \ > 3 else \"\",\r\n        \"combined_image_path\": combined_url,\r\n \
          \       \"markdown_pic_url\": markdown_pic_url\r\n    }"
        code_language: python3
        desc: ''
        outputs:
          combined_image_path:
            children: null
            type: string
          markdown_pic_url:
            children: null
            type: string
          pic_url:
            children: null
            type: array[string]
          pic_url1:
            children: null
            type: string
          pic_url2:
            children: null
            type: string
          pic_url3:
            children: null
            type: string
          pic_url4:
            children: null
            type: string
        selected: false
        title: 修图返回数据处理
        type: code
        variables:
        - value_selector:
          - '17471327230020'
          - body
          variable: arg1
      height: 53
      id: '17471369319000'
      position:
        x: 3119.379419373741
        y: 318.0429286671252
      positionAbsolute:
        x: 3119.379419373741
        y: 318.0429286671252
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: aeb22881-b258-47d8-b1a5-b8d8a1f65430
            value: 全部大图
            varType: string
            variable_selector:
            - sys
            - query
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 是否返回全部大图
        type: if-else
      height: 125
      id: '1747142489643'
      position:
        x: 3694.4993233798496
        y: 869.2760497651395
      positionAbsolute:
        x: 3694.4993233798496
        y: 869.2760497651395
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1747055122415.markdown_pic_url#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 104
      id: '1747142591712'
      position:
        x: 4279.845599699689
        y: 920.5277000537271
      positionAbsolute:
        x: 4279.845599699689
        y: 920.5277000537271
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1747055122415'
          - pic_url
          variable_selector:
          - conversation
          - pic_url
          write_mode: over-write
        selected: false
        title: 变量赋值
        type: assigner
        version: '2'
      height: 87
      id: '17471427081180'
      position:
        x: 3686.012939323409
        y: 1058.9708080417547
      positionAbsolute:
        x: 3686.012939323409
        y: 1058.9708080417547
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '17471369319000'
          - pic_url
          variable_selector:
          - conversation
          - pic_url
          write_mode: over-write
        selected: false
        title: 变量赋值
        type: assigner
        version: '2'
      height: 87
      id: '17471427431470'
      position:
        x: 3520.9339550204745
        y: 280.0820704956503
      positionAbsolute:
        x: 3520.9339550204745
        y: 280.0820704956503
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: aeb22881-b258-47d8-b1a5-b8d8a1f65430
            value: 全部大图
            varType: string
            variable_selector:
            - sys
            - query
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 是否返回全部大图
        type: if-else
      height: 125
      id: '17471428550290'
      position:
        x: 3560.703343263692
        y: 368.48129547831593
      positionAbsolute:
        x: 3560.703343263692
        y: 368.48129547831593
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '![image]({{#17471369319000.combined_image_path#}})'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 120
      id: '17471428735390'
      position:
        x: 3984.13471909107
        y: 354.7904035499088
      positionAbsolute:
        x: 3984.13471909107
        y: 354.7904035499088
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#17471369319000.markdown_pic_url#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 104
      id: '17471428936900'
      position:
        x: 3990.2156543576466
        y: 476.05430838778545
      positionAbsolute:
        x: 3990.2156543576466
        y: 476.05430838778545
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import re\n\ndef main(arg1: str, arg2: str) -> dict:\n    # 处理arg1：生成MinIO\
          \ URL\n    minio_url = \"http://minio.hncaa.cn:19000/dify\" + arg1.replace('/app/api/storage',\
          \ '')\n    \n    # 处理arg2：过滤指定模式\n    patterns = [\n        r'第.*?张',  #\
          \ 匹配 \"第*张\"\n        r'第.*?个',  # 匹配 \"第*个\"\n        r'首张|首个|头张|头个|头一张|头一个'\
          \  # 匹配固定字符串\n    ]\n    result = arg2\n    for pattern in patterns:\n \
          \       result = re.sub(pattern, '', result)\n    \n    # 返回结果\n    return\
          \ {\n        \"file_url\": minio_url,\n        \"result\": result\n    }"
        code_language: python3
        desc: ''
        outputs:
          file_url:
            children: null
            type: string
          result:
            children: null
            type: string
        selected: false
        title: 图片文件修图参数提取
        type: code
        variables:
        - value_selector:
          - '1747119436299'
          - first_record
          - url
          variable: arg1
        - value_selector:
          - sys
          - query
          variable: arg2
      height: 53
      id: '1747156806866'
      position:
        x: 1469.4624496761082
        y: 216.32904874404716
      positionAbsolute:
        x: 1469.4624496761082
        y: 216.32904874404716
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import re\n\ndef main(arg1: str) -> dict:\n    # 定义图片URL的正则表达式，支持webp、bmp、查询参数和锚点\n\
          \    url_pattern = r'https?://[^\\s/]+(?:/[^\\s/]+)*?\\.(?:jpg|jpeg|png|gif|webp|bmp)\\\
          b(?:\\?[^\\s#]*)?(?:#[^\\s]*)?'\n    \n    # 提取图片URL\n    url_match = re.search(url_pattern,\
          \ arg1)\n    pic_url = url_match.group(0) if url_match else \"\"\n    \n\
          \    # 移除URL后的文本内容\n    prompt = re.sub(url_pattern, '', arg1).strip()\n\
          \    \n    # 返回结果\n    return {\n        \"pic_url\": pic_url,\n       \
          \ \"prompt\": prompt\n    }"
        code_language: python3
        desc: ''
        outputs:
          pic_url:
            children: null
            type: string
          prompt:
            children: null
            type: string
        selected: false
        title: url修图参数提取
        type: code
        variables:
        - value_selector:
          - sys
          - query
          variable: arg1
      height: 53
      id: '1747157225083'
      position:
        x: 1184.3950356725184
        y: 373.8153717539854
      positionAbsolute:
        x: 1184.3950356725184
        y: 373.8153717539854
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        classes:
        - id: '1'
          name: 既有图片中选择图片进行发送/放大意图（高优先级）
        - id: '2'
          name: 既有图片基础上修改图片意图（不含放大图片）
        - id: '1747368070225'
          name: 基于文本绘制/生成新图片意图
        desc: ''
        instructions: ''
        model:
          completion_params:
            temperature: 0
          mode: chat
          name: qwen2.5-3b-instruct
          provider: langgenius/tongyi/tongyi
        query_variable_selector:
        - '1747052157185'
        - sys.query
        selected: false
        title: 问题分类器 (2)
        topics: []
        type: question-classifier
        vision:
          enabled: false
      height: 241
      id: '17473687599390'
      position:
        x: 888.8316649002631
        y: 507
      positionAbsolute:
        x: 888.8316649002631
        y: 507
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(arg1: str, arg2: str) -> dict:\n    # 直接使用arg1作为图片URL列表\n\
          \    pic_urls = arg1\n    \n    # 初始化返回的URL和Markdown格式\n    selected_url\
          \ = \"\"\n    markdown_selected_url = \"\"\n    \n    # 如果pic_urls不为空，处理关键字匹配\n\
          \    if pic_urls:\n        # 转换为小写以忽略大小写\n        arg2_lower = arg2.lower()\n\
          \        \n        # 检查关键字并提取对应URL\n        if any(keyword in arg2_lower\
          \ for keyword in [\"第一张\", \"第1张\", \"第一个\", \"第1个\", \"首张\", \"首个\", \"\
          头张\", \"头个\", \"头一张\", \"头一个\"]):\n            selected_url = pic_urls[0]\
          \ if len(pic_urls) > 0 else \"\"\n            markdown_selected_url = f\"\
          ![image]({selected_url})\" if selected_url else \"\"\n        elif any(keyword\
          \ in arg2_lower for keyword in [\"第二张\", \"第2张\", \"第二个\", \"第2个\"]):\n\
          \            selected_url = pic_urls[1] if len(pic_urls) > 1 else \"\"\n\
          \            markdown_selected_url = f\"![image]({selected_url})\" if selected_url\
          \ else \"\"\n        elif any(keyword in arg2_lower for keyword in [\"第三张\"\
          , \"第3张\", \"第三个\", \"第3个\"]):\n            selected_url = pic_urls[2] if\
          \ len(pic_urls) > 2 else \"\"\n            markdown_selected_url = f\"![image]({selected_url})\"\
          \ if selected_url else \"\"\n        elif \"全部大图\" in arg2_lower:\n    \
          \        selected_url = \"\"  # 显式返回空字符串\n            # 取最后四个URL，构造Markdown格式\n\
          \            last_four_urls = pic_urls[-4:] if len(pic_urls) >= 4 else pic_urls\n\
          \            markdown_lines = [f\"![image{i+1}]({url})\" for i, url in enumerate(last_four_urls)]\n\
          \            markdown_selected_url = \"\\n\".join(markdown_lines) if markdown_lines\
          \ else \"\"\n        else:\n            # 默认返回最后一个URL\n            selected_url\
          \ = pic_urls[-1] if pic_urls else \"\"\n            markdown_selected_url\
          \ = f\"![image]({selected_url})\" if selected_url else \"\"\n    \n    return\
          \ {\n        \"selected_url\": selected_url,\n        \"markdown_selected_url\"\
          : markdown_selected_url\n    }"
        code_language: python3
        desc: ''
        outputs:
          markdown_selected_url:
            children: null
            type: string
          selected_url:
            children: null
            type: string
        selected: false
        title: 目标图片选择
        type: code
        variables:
        - value_selector:
          - conversation
          - pic_url
          variable: arg1
        - value_selector:
          - sys
          - query
          variable: arg2
      height: 53
      id: '17473688504210'
      position:
        x: 1262.077020261218
        y: 488
      positionAbsolute:
        x: 1262.077020261218
        y: 488
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(arg1: str, arg2: str) -> dict:\n    # 直接使用arg1作为图片URL列表\n\
          \    pic_urls = arg1\n    \n    # 初始化返回的URL和Markdown格式\n    selected_url\
          \ = \"\"\n    markdown_selected_url = \"\"\n    \n    # 如果pic_urls不为空，处理关键字匹配\n\
          \    if pic_urls:\n        # 转换为小写以忽略大小写\n        arg2_lower = arg2.lower()\n\
          \        \n        # 检查关键字并提取对应URL\n        if any(keyword in arg2_lower\
          \ for keyword in [\"第一张\", \"第1张\", \"第一个\", \"第1个\", \"首张\", \"首个\", \"\
          头张\", \"头个\", \"头一张\", \"头一个\"]):\n            selected_url = pic_urls[0]\
          \ if len(pic_urls) > 0 else \"\"\n            markdown_selected_url = f\"\
          ![image]({selected_url})\" if selected_url else \"\"\n        elif any(keyword\
          \ in arg2_lower for keyword in [\"第二张\", \"第2张\", \"第二个\", \"第2个\"]):\n\
          \            selected_url = pic_urls[1] if len(pic_urls) > 1 else \"\"\n\
          \            markdown_selected_url = f\"![image]({selected_url})\" if selected_url\
          \ else \"\"\n        elif any(keyword in arg2_lower for keyword in [\"第三张\"\
          , \"第3张\", \"第三个\", \"第3个\"]):\n            selected_url = pic_urls[2] if\
          \ len(pic_urls) > 2 else \"\"\n            markdown_selected_url = f\"![image]({selected_url})\"\
          \ if selected_url else \"\"\n        elif \"全部大图\" in arg2_lower:\n    \
          \        selected_url = \"\"  # 显式返回空字符串\n            # 取最后四个URL，构造Markdown格式\n\
          \            last_four_urls = pic_urls[-4:] if len(pic_urls) >= 4 else pic_urls\n\
          \            markdown_lines = [f\"![image{i+1}]({url})\" for i, url in enumerate(last_four_urls)]\n\
          \            markdown_selected_url = \"\\n\".join(markdown_lines) if markdown_lines\
          \ else \"\"\n        else:\n            # 默认返回最后一个URL\n            selected_url\
          \ = pic_urls[-1] if pic_urls else \"\"\n            markdown_selected_url\
          \ = f\"![image]({selected_url})\" if selected_url else \"\"\n    \n    return\
          \ {\n        \"selected_url\": selected_url,\n        \"markdown_selected_url\"\
          : markdown_selected_url\n    }"
        code_language: python3
        desc: ''
        outputs:
          markdown_selected_url:
            children: null
            type: string
          selected_url:
            children: null
            type: string
        selected: false
        title: 目标图片选择
        type: code
        variables:
        - value_selector:
          - conversation
          - pic_url
          variable: arg1
        - value_selector:
          - sys
          - query
          variable: arg2
      height: 53
      id: '17473689783710'
      position:
        x: 1223.0328281982602
        y: 643.8737374413919
      positionAbsolute:
        x: 1223.0328281982602
        y: 643.8737374413919
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#17473689783710.markdown_selected_url#}}'
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 104
      id: '17473690032030'
      position:
        x: 1531.626262558608
        y: 616.5991162926967
      positionAbsolute:
        x: 1531.626262558608
        y: 616.5991162926967
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import re\n\ndef main(arg1: str) -> dict:\n    # 定义需要过滤的模式\n    patterns\
          \ = [\n        r'第.*?张',  # 匹配 \"第*张\"\n        r'第.*?个',  # 匹配 \"第*个\"\n\
          \        r'首张|首个|头张|头个|头一张|头一个'  # 匹配固定字符串\n    ]\n    \n    # 过滤字符串\n \
          \   result = arg1\n    for pattern in patterns:\n        result = re.sub(pattern,\
          \ '', result)\n    \n    # 返回结果\n    return {\n        \"result\": result\n\
          \    }"
        code_language: python3
        desc: ''
        outputs:
          result:
            children: null
            type: string
        selected: false
        title: 既有图片修图指令过滤
        type: code
        variables:
        - value_selector:
          - sys
          - query
          variable: arg1
      height: 53
      id: '17473690296460'
      position:
        x: 1550
        y: 488
      positionAbsolute:
        x: 1550
        y: 488
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        classes:
        - id: '1'
          name: 清晰化图片意图（用户提及单张参考图）
        - id: '1748269921682'
          name: 更换图片主体意图（用户提及单张参考图）
        - id: '1748270192882'
          name: 更换图片背景意图（用户提及单张参考图）
        - id: '1748270200241'
          name: 抠除图片背景意图（用户提及单张参考图）
        - id: '1748270255153'
          name: 对原图单个图片进行区域重绘（用户提及单张参考图）
        - id: '1748270263201'
          name: 结合两张图片进行区域重绘或融合（用户提及两张参考图）
        - id: '1748270273625'
          name: 换脸相关绘图意图
        - id: '1748270317191'
          name: 基于参考图生成新图片，且有明确指定角色特征（用户提及单张参考图）
        - id: '1748270344610'
          name: 基于参考图生成新图片，且明确指定需要多张（用户提及单张参考图）
        - id: '1748429742024'
          name: 通用基于参考图生成新图片意图（用户提及单张参考图）
        desc: ''
        instructions: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: gemini-2.0-flash
          provider: langgenius/openai_api_compatible/openai_api_compatible
        query_variable_selector:
        - '1747052157185'
        - sys.query
        selected: false
        title: 图生图分类
        topics: []
        type: question-classifier
        vision:
          enabled: false
      height: 619
      id: '17473943558420'
      position:
        x: 2139.9510716090736
        y: 73.20865055645442
      positionAbsolute:
        x: 2139.9510716090736
        y: 73.20865055645442
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: "你是一名专业的 AI 图像修改参数构建专家，你的任务是根据用户图像修改意图，提取图像修改的参数，提取的参数及判定规则如下：\n\
          \n## \"reference_mode\"参数（用户修改图像的模式）: \n可选值：byte_edit 或 ip_keep；在用户意图为修改图片中人物的角色特征或指定横/竖屏属性时，使用ip_keep；其他情况使用默认值\
          \ byte_edit\"\n\n## \"width\"和\"height\"参数（生成图像的宽度和高度）: \n- 当\"reference_mode\"\
          参数为 byte_edit 时，\"width\"和\"height\"必须留空）\"；\n- 当\"reference_mode\"参数为 ip_keep\
          \ 时，\"width\"和\"height\"根据用户指定的横/竖屏属性判定：横屏：1280*720；竖屏：720*1280，如用户未指定，则默认留空"
        model:
          completion_params:
            temperature: 0
          mode: chat
          name: qwen2.5-3b-instruct
          provider: langgenius/tongyi/tongyi
        parameters:
        - description: 图生图使用的参考图模式
          name: reference_mode
          required: true
          type: string
        - description: 生成图像的宽度
          name: width
          required: true
          type: string
        - description: 生成图像的高度
          name: height
          required: true
          type: string
        query:
        - '1747119741130'
        - prompt
        - output
        reasoning_mode: prompt
        selected: false
        title: 通用修图参数提取
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 89
      id: '17473943711060'
      position:
        x: 2534.899416282159
        y: 216.32904874404716
      positionAbsolute:
        x: 2534.899416282159
        y: 216.32904874404716
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        authorization:
          config: null
          type: no-auth
        body:
          data:
          - id: key-value-249
            key: ''
            type: text
            value: "{\n\t\"prompt\": \"{{#1748234699992.text#}}\",\n\t\"model\": \"\
              txt2pic\",\n\t\"stream\": false\n}"
          type: json
        desc: ''
        headers: 'Authorization:Bearer {{#env.doubao_apikey#}}

          Content-Type:application/json'
        method: post
        params: ''
        retry_config:
          max_retries: 1
          retry_enabled: false
          retry_interval: '1000'
        selected: false
        ssl_verify: true
        timeout:
          connect: 180
          max_connect_timeout: 0
          max_read_timeout: 0
          max_write_timeout: 0
          read: 180
          write: 180
        title: DB文生图
        type: http-request
        url: http://************:8062/v1/images/generate
        variables: []
      height: 109
      id: '17482321830520'
      position:
        x: 2551.0089132182616
        y: 1058.9708080417547
      positionAbsolute:
        x: 2551.0089132182616
        y: 1058.9708080417547
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '用户绘图指令：{{#1748259643500.output#}}

            用户原始输入：{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 50
        model:
          completion_params: {}
          mode: chat
          name: gpt-4o-mini
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 6ca202fc-222a-4cd2-922d-6809b639aeef
          role: system
          text: "# Role: AI绘画和提示词修改专家，你的任务是根据“用户绘图指令”，分析提取/构建“绘图风格”、“绘图提示词”、“绘图比例”三个部分，构建完整且高质量的绘图提示词，然后按以下格式输出：\n\
            图片风格为「绘图风格」，比例 「绘图比例」绘图提示词（此处直接使用绘图提示词，禁止使用“绘图提示词”字眼）\n\n\n## 根据“用户绘图指令”中的绘图意图判定“绘图风格”：\n\
            - 绘图风格可选值：\n人像摄影/电影写真/中国风/动漫/3D渲染/赛博朋克/CG动画/水墨画/油画/古典/水彩画/卡通/平面插画/风景/港风动漫/像素风格/荧光绘画/彩铅画/手办/儿童绘画/抽象/锐笔插画/二次元/油墨印刷/版画/莫奈/毕加索/伦勃朗/马蒂斯/巴洛克/复古动漫/绘本\n\
            - 如判断未果，则默认为：人像摄影\n\n## 如果“用户绘图指令”中已经对绘图对象有描述内容（如：画一个女生图书馆看书，女生为年轻女性，黑色长发披肩，身着淡蓝色连衣裙。画面风格是具有细腻笔触、丰富色彩和真实光影效果的写实风），则保持用户原绘图提示词不变；如果“用户绘图指令”中没有对绘图对象的描述（如：画一个狗狗），则按以下规则扩展构建“绘图提示词”：\n\
            \n1. **识别核心主体与意图：** 准确理解用户想画什么，抓住最关键的人物、事物或场景。\n2. **细化主体特征：**\n   * **人物/动物：**\
            \ 补充外貌（发型、五官、肤色）、神态表情、动作姿态、服装风格与细节、佩戴饰品。\n   * **景物/物品：** 补充形态、材质、纹理、颜色、所处环境。\n\
            3. **描绘环境背景：** 设定主体所处的具体环境（室内、室外、自然风光、城市街景）或氛围背景（抽象色彩、光影效果），增加画面的故事感和深度。\n\
            4. **设定光影效果：** 明确光线来源、强度、色调（如：午后阳光、霓虹灯光、柔和月光、电影感光效），营造特定氛围。\n5. **确定构图与视角：**\
            \ 选择合适的视角（仰视、俯视、平视）和景别（特写、近景、远景），指导画面布局。\n6. **丰富色彩与细节：** 指定主色调或色彩搭配，添加能提升画面丰富度的细节元素（如：飘落的花瓣、远处的飞鸟、桌面上的小物件）。\n\
            7. **加入品质提升词：** 适时添加通用高品质词汇，如：“杰作”、“最佳质量”、“超精细”、“高细节”、“逼真”、“锐利对焦”、“8K”、“电影感”、“氛围感”。\n\
            8. **组合成流畅描述：** 将以上要素有机地组织起来，形成一段或多段清晰、具体、富有想象力的描述性文字，确保关键词用逗号分隔，使AI能够准确理解并执行。\n\
            \n## 分析“用户绘图指令”中的绘图意图，根据绘图意图按以下规则判定“绘图比例”：\n\n- 方屏/头像/正方形: 1:1\n- 社交媒体/自拍:\
            \ 2:3\n- 插画/文章配图: 4:3\n- 竖屏/手机壁纸/人像: 9:16\n- 横屏/桌面壁纸/风景: 16:9\n- 如以上判断无果，则默认：1:1\n\
            \n# 输出格式：图片风格为「绘图风格」，比例 「绘图比例」绘图提示词（此处直接使用绘图提示词，禁止使用“绘图提示词”字眼）\n\n# 示例输出：图片风格为「人像摄影」，比例\
            \ 「9:16」画一位一位大约20岁的亚洲女性，拥有长长的波浪状棕色头发和明亮的眼睛，面带温柔的微笑。她穿着一件简约的白色连衣裙，赤脚坐在夏末午后宁静的森林边缘草地上。温暖的阳光从侧后方照射下来，在她身上形成柔和的轮廓光，光线穿过树叶，在背景中投下斑驳的光斑和树影，远处点缀着些许野花。中景构图，平视视角，她自然放松地坐着，眼神望向远方。画面呈现出宁静、温暖的氛围，具有电影感光效和景深效果，杰作，最佳质量，逼真，锐利对焦。\n\
            \n# 约束：最终输出且仅输出格式化后的完整提示词，不要输出解释、说明等其他任何内容。"
        selected: false
        title: 豆包绘图参数生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1748234699992'
      position:
        x: 2043.8241277293719
        y: 1190.4531045425526
      positionAbsolute:
        x: 2043.8241277293719
        y: 1190.4531045425526
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        output_type: string
        selected: false
        title: 绘图API返回数据聚合
        type: variable-aggregator
        variables:
        - - '1747052169396'
          - body
        - - '17482321830520'
          - body
      height: 130
      id: '1748253304926'
      position:
        x: 2897.372547104336
        y: 1007.151528355183
      positionAbsolute:
        x: 2897.372547104336
        y: 1007.151528355183
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        instruction: "#你是一名专业的 AI绘画和提示词修改专家，你的任务是基于“用户绘图指令”，按照以下规则，分析提取/构建图片生成所需的绘图提示词、绘图反向提示词、待生成图片的宽度和高度：\n\
          \n## \"prompt\": \"绘图提示词\"：\n如果“用户绘图指令”中已经对绘图对象有描述内容（如：画一个女生图书馆看书，女生为年轻女性，黑色长发披肩，身着淡蓝色连衣裙。画面风格是具有细腻笔触、丰富色彩和真实光影效果的写实风），则保持用户原绘图提示词不变；如果“用户绘图指令”中没有对绘图对象的描述（如：画一个狗狗），则按以下规则扩展构建“绘图提示词”：\n\
          \n1. **识别核心主体与意图：** 准确理解用户想画什么，抓住最关键的人物、事物或场景。\n2. **细化主体特征：**\n   * **人物/动物：**\
          \ 补充外貌（发型、五官、肤色）、神态表情、动作姿态、服装风格与细节、佩戴饰品。\n   * **景物/物品：** 补充形态、材质、纹理、颜色、所处环境。\n\
          3. **描绘环境背景：** 设定主体所处的具体环境（室内、室外、自然风光、城市街景）或氛围背景（抽象色彩、光影效果），增加画面的故事感和深度。\n\
          4. **设定光影效果：** 明确光线来源、强度、色调（如：午后阳光、霓虹灯光、柔和月光、电影感光效），营造特定氛围。\n5. **确定构图与视角：**\
          \ 选择合适的视角（仰视、俯视、平视）和景别（特写、近景、远景），指导画面布局。\n6. **丰富色彩与细节：** 指定主色调或色彩搭配，添加能提升画面丰富度的细节元素（如：飘落的花瓣、远处的飞鸟、桌面上的小物件）。\n\
          7. **加入品质提升词：** 适时添加通用高品质词汇，如：“杰作”、“最佳质量”、“超精细”、“高细节”、“逼真”、“锐利对焦”、“8K”、“电影感”、“氛围感”。\n\
          8. **组合成流畅描述：** 将以上要素有机地组织起来，形成一段或多段清晰、具体、富有想象力的描述性文字，确保关键词用逗号分隔，使AI能够准确理解并执行。\n\
          \n## \"negative_prompt\": \"绘图反向提示词\"：“用户绘图指令”中明确提出禁止/约束的内容，默认为：模糊，低质量\n\
          \n## \"width\": \"待生成图片的宽度\"；\"height\": \"待生成图片的高度\"：\n\n- 方屏/头像/正方形: \"\
          width\": 1328；\"height\": 1328\n- 社交媒体/自拍: \"width\": 1056；\"height\": 1584\n\
          - 插画/文章配图: \"width\": 1472；\"height\": 1104\n- 竖屏/手机壁纸/人像: \"width\": 936；\"\
          height\": 1664\n- 横屏/桌面壁纸/风景: \"width\": 1664；\"height\": 936\n- 如以上判断无果，则默认：\"\
          width\": 1328；\"height\": 1328"
        memory:
          query_prompt_template: '{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 50
        model:
          completion_params: {}
          mode: chat
          name: gpt-4o-mini
          provider: langgenius/openai_api_compatible/openai_api_compatible
        parameters:
        - description: 绘图提示词
          name: prompt
          required: true
          type: string
        - description: 绘图反向提示词
          name: negative_prompt
          required: true
          type: string
        - description: 待生成图片的宽度
          name: width
          required: true
          type: string
        - description: 待生成图片的高度
          name: height
          required: true
          type: string
        query:
        - '1748260255939'
        - output
        reasoning_mode: prompt
        selected: false
        title: 即梦绘图参数生成
        type: parameter-extractor
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1748253720316'
      position:
        x: 2048.8134923517805
        y: 1022.1802357666386
      positionAbsolute:
        x: 2048.8134923517805
        y: 1022.1802357666386
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "{%- if arg1.startswith('豆包') -%}\r\n{{- arg1[2:] -}}\r\n{%- else\
          \ -%}\r\n{{- arg1 -}}\r\n{%- endif -%}"
        title: 过滤豆包
        type: template-transform
        variables:
        - value_selector:
          - sys
          - query
          variable: arg1
      height: 53
      id: '1748259643500'
      position:
        x: 1586.9675072927703
        y: 1137.0186367724205
      positionAbsolute:
        x: 1586.9675072927703
        y: 1137.0186367724205
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "{%- if arg1.startswith('即梦') -%}\r\n{{- arg1[2:] -}}\r\n{%- else\
          \ -%}\r\n{{- arg1 -}}\r\n{%- endif -%}"
        title: 过滤即梦
        type: template-transform
        variables:
        - value_selector:
          - sys
          - query
          variable: arg1
      height: 53
      id: '1748260255939'
      position:
        x: 1616.8471187067466
        y: 1047.9208400628115
      positionAbsolute:
        x: 1616.8471187067466
        y: 1047.9208400628115
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: contains
            id: a17ef482-e63b-4846-86e0-d1d20a948bab
            value: 小红书风格
            varType: string
            variable_selector:
            - sys
            - query
          - comparison_operator: contains
            id: f0902f4d-7f47-4beb-b154-da589b234bca
            value: 写实风格
            varType: string
            variable_selector:
            - sys
            - query
          id: 'true'
          logical_operator: or
        - case_id: d06199e5-1b54-4936-9e87-31f8b53a8db6
          conditions:
          - comparison_operator: contains
            id: 4885bc1d-c6e1-425e-b421-30e5d07095be
            value: 谷歌
            varType: string
            variable_selector:
            - sys
            - query
          id: d06199e5-1b54-4936-9e87-31f8b53a8db6
          logical_operator: and
        - case_id: 0ea0397e-037e-4af5-b19c-1c55a999b4af
          conditions:
          - comparison_operator: contains
            id: 74770879-0dcc-421d-a3dd-20fc7fed2d3c
            value: 图文
            varType: string
            variable_selector:
            - sys
            - query
          - comparison_operator: contains
            id: b20cd257-7ce5-46ba-8e8b-9d8f10e79b97
            value: 多场景
            varType: string
            variable_selector:
            - sys
            - query
          id: 0ea0397e-037e-4af5-b19c-1c55a999b4af
          logical_operator: or
        - case_id: 3c4a809a-9f75-49ea-896a-1eba690163f2
          conditions:
          - comparison_operator: contains
            id: 5a3d19c4-8d50-4c05-ab6b-231319157ced
            value: 即梦
            varType: string
            variable_selector:
            - sys
            - query
          id: 3c4a809a-9f75-49ea-896a-1eba690163f2
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 5
        type: if-else
      height: 321
      id: '1748264121958'
      position:
        x: 1113.2589431305287
        y: 890.2535273554239
      positionAbsolute:
        x: 1113.2589431305287
        y: 890.2535273554239
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gpt-4o-mini
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: a13dd0b0-4436-4b50-a996-ab79df259272
          role: system
          text: "# 你是一名专业的 AI 图像生成提示词构建专家，你的任务是根据用户的绘图意图，构建完整且高质量的英文提示词 `prompt`，并将其组织成结构化的\
            \ JSON 格式输出。\n输出格式：\n{\n  \"workflow_name\": \"工作流类型名称\",\n  \"prompt\"\
            : \"英文提示词\",\n  \"urls\": \"\",\n  \"width\": \"图像宽度\",\n  \"height\"\
            : \"图像高度\",\n  \"show_url\": \"是否显示URL(默认false，如果用户明确指定发送URL，则设置为true)\"\
            \n}\n\n## \"workflow_name\"（工作流）判定规则：\n\n- 小红书风格：txt_xhs\n- 写实风格：txt_boreal\n\
            \n\n## \"prompt\"（英文提示词）构建规范：\n\n如果用户提供了英文提示词，则直接使用，无需重新构建。否则按以下规则构建：\n\
            \n1. **识别核心主体与意图：** 准确理解用户想画什么，抓住最关键的人物、事物或场景。\n2. **细化主体特征：**\n   * **人物/动物：**\
            \ 补充外貌（发型、五官、肤色）、神态表情、动作姿态、服装风格与细节、佩戴饰品。\n   * **景物/物品：** 补充形态、材质、纹理、颜色、所处环境。\n\
            3. **描绘环境背景：** 设定主体所处的具体环境（室内、室外、自然风光、城市街景）或氛围背景（抽象色彩、光影效果），增加画面的故事感和深度。\n\
            4. **设定光影效果：** 明确光线来源、强度、色调（如：午后阳光、霓虹灯光、柔和月光、电影感光效），营造特定氛围。\n5. **确定构图与视角：**\
            \ 选择合适的视角（仰视、俯视、平视）和景别（特写、近景、远景），指导画面布局。\n6. **丰富色彩与细节：** 指定主色调或色彩搭配，添加能提升画面丰富度的细节元素（如：飘落的花瓣、远处的飞鸟、桌面上的小物件）。\n\
            7. **加入品质提升词：** 适时添加通用高品质词汇，如：“杰作”、“最佳质量”、“超精细”、“高细节”、“逼真”、“锐利对焦”、“8K”、“电影感”、“氛围感”。\n\
            8. **组合成流畅描述：** 将以上要素有机地组织起来，形成一段或多段清晰、具体、富有想象力的描述性文字，确保关键词用逗号分隔，使AI能够准确理解并执行。\n\
            \n注意：prompt中不得包含小红书风格、写实风格等风格类词语！\n\n## \"urls\": \"\": urls处必须留空，禁止随意填写。\n\
            \n## \"width\"、\"height\"（图像尺寸）判定规则：\n- 优先使用用户指定尺寸\n- 如用户没有指定尺寸，可根据绘图意图（例如：风景照，手机壁纸）判断出横、竖、方屏属性\n\
            - 如以上判断无果，则默认：1024*1024\n- 横屏：1280*800\n- 竖屏：800*1280\n- 方屏：1024*1024\n\
            \n# 强制约束：\n1. workflow_name必须为以下之一：txt_xhs、txt_boreal\n2. 不满足上述任何一个时，强默认使用txt_boreal\n\
            3. prompt中不得包含小红书风格、写实风格等风格类词语\n4. urls处必须留空，禁止随意填写"
        selected: false
        structured_output:
          schema:
            additionalProperties: false
            properties:
              height:
                type: string
              prompt:
                type: string
              show_url:
                type: string
              urls:
                type: string
              width:
                type: string
              workflow_name:
                type: string
            required:
            - workflow_name
            - prompt
            - urls
            - width
            - height
            - show_url
            type: object
        structured_output_enabled: true
        title: COMFY绘图参数生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1748265333227'
      position:
        x: 1677.2865831846589
        y: 771.9904163001373
      positionAbsolute:
        x: 1677.2865831846589
        y: 771.9904163001373
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "import json\nimport json_repair\nfrom typing import Dict, Any\n\ndef\
          \ main(json_str: str) -> Dict[str, Any]:\n    # 修复 JSON 字符串\n    json_str\
          \ = json_repair.repair_json(json_str, ensure_ascii=False)\n\n    data =\
          \ json.loads(json_str)\n    prompt = data[\"prompt\"]\n    urls = data.get(\"\
          urls\", \"\")\n    show_url = data.get(\"show_url\", False)\n    if isinstance(show_url,\
          \ str):\n         show_url = show_url.lower() == \"true\"\n    workflow_name\
          \ = data[\"workflow_name\"]\n    width = data.get(\"width\", 1024)\n   \
          \ height = data.get(\"height\", 1024)\n\n    max_size = 1280\n    if width\
          \ > max_size or height > max_size:\n        if width > height:\n       \
          \     ratio = max_size / width\n            width = max_size\n         \
          \   height = int(height * ratio)\n        else:\n            ratio = max_size\
          \ / height\n            height = max_size\n            width = int(width\
          \ * ratio)\n\n\n    results = {\n         \"prompt\": prompt,\n        \
          \ \"workflow_name\": workflow_name,\n         \"width\": width,\n      \
          \   \"height\": height,\n         \"show_url\": show_url,\n         \"urls\"\
          : urls\n     }\n\n    return results"
        code_language: python3
        desc: ''
        outputs:
          height:
            children: null
            type: number
          prompt:
            children: null
            type: string
          show_url:
            children: null
            type: string
          urls:
            children: null
            type: string
          width:
            children: null
            type: number
          workflow_name:
            children: null
            type: string
        selected: false
        title: COMFY绘图参数提取
        type: code
        variables:
        - value_selector:
          - '1748265333227'
          - text
          variable: arg1
      height: 53
      id: '1748265816085'
      position:
        x: 2134.8987738642318
        y: 771.9904163001373
      positionAbsolute:
        x: 2134.8987738642318
        y: 771.9904163001373
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 工作流名称
            ja_JP: 工作流名称
            pt_BR: 工作流名称
            zh_Hans: 工作流名称
          label:
            en_US: workflow_name
            ja_JP: workflow_name
            pt_BR: workflow_name
            zh_Hans: workflow_name
          llm_description: 工作流名称
          max: null
          min: null
          name: workflow_name
          options: []
          placeholder:
            en_US: 工作流名称
            ja_JP: 工作流名称
            pt_BR: 工作流名称
            zh_Hans: 工作流名称
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 生成提示词
            ja_JP: 生成提示词
            pt_BR: 生成提示词
            zh_Hans: 生成提示词
          label:
            en_US: prompt
            ja_JP: prompt
            pt_BR: prompt
            zh_Hans: prompt
          llm_description: 生成提示词
          max: null
          min: null
          name: prompt
          options: []
          placeholder:
            en_US: 生成提示词
            ja_JP: 生成提示词
            pt_BR: 生成提示词
            zh_Hans: 生成提示词
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: 1024
          form: llm
          human_description:
            en_US: 生成图片宽度
            ja_JP: 生成图片宽度
            pt_BR: 生成图片宽度
            zh_Hans: 生成图片宽度
          label:
            en_US: width
            ja_JP: width
            pt_BR: width
            zh_Hans: width
          llm_description: 生成图片宽度
          max: null
          min: null
          name: width
          options: []
          placeholder:
            en_US: 生成图片宽度
            ja_JP: 生成图片宽度
            pt_BR: 生成图片宽度
            zh_Hans: 生成图片宽度
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: 1024
          form: llm
          human_description:
            en_US: 生成图片高度
            ja_JP: 生成图片高度
            pt_BR: 生成图片高度
            zh_Hans: 生成图片高度
          label:
            en_US: height
            ja_JP: height
            pt_BR: height
            zh_Hans: height
          llm_description: 生成图片高度
          max: null
          min: null
          name: height
          options: []
          placeholder:
            en_US: 生成图片高度
            ja_JP: 生成图片高度
            pt_BR: 生成图片高度
            zh_Hans: 生成图片高度
          precision: null
          required: false
          scope: null
          template: null
          type: number
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 输入图片URL,多个URL用|分隔
            ja_JP: 输入图片URL,多个URL用|分隔
            pt_BR: 输入图片URL,多个URL用|分隔
            zh_Hans: 输入图片URL,多个URL用|分隔
          label:
            en_US: urls
            ja_JP: urls
            pt_BR: urls
            zh_Hans: urls
          llm_description: 输入图片URL,多个URL用|分隔
          max: null
          min: null
          name: urls
          options: []
          placeholder:
            en_US: 输入图片URL,多个URL用|分隔
            ja_JP: 输入图片URL,多个URL用|分隔
            pt_BR: 输入图片URL,多个URL用|分隔
            zh_Hans: 输入图片URL,多个URL用|分隔
          precision: null
          required: false
          scope: null
          template: null
          type: string
        params:
          height: ''
          prompt: ''
          urls: ''
          width: ''
          workflow_name: ''
        provider_id: 5bb6db18-c97f-430a-b97f-9930bb8db25a
        provider_name: COMFY_GEN_V2
        provider_type: api
        selected: false
        title: comfy_gen_v2
        tool_configurations: {}
        tool_description: 通用 ComfyUI 生成接口 V2
        tool_label: comfy_gen_v2
        tool_name: comfy_gen_v2
        tool_parameters:
          height:
            type: variable
            value:
            - '1748265816085'
            - height
          prompt:
            type: mixed
            value: '{{#1748265816085.prompt#}}'
          show_url:
            type: mixed
            value: '{{#1748265816085.show_url#}}'
          urls:
            type: mixed
            value: '{{#1748265816085.urls#}}'
          width:
            type: variable
            value:
            - '1748265816085'
            - width
          workflow_name:
            type: mixed
            value: '{{#1748265816085.workflow_name#}}'
        type: tool
      height: 53
      id: '1748266385503'
      position:
        x: 2386.4776262350833
        y: 709.4557807604625
      positionAbsolute:
        x: 2386.4776262350833
        y: 709.4557807604625
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "openapi: 3.1.0\ninfo:\n  title: FastAPI\n  version: 0.1.0\n  description:\
          \ 通用 ComfyUI 生成接口 V2的api\nservers:\n  - url: 'http://************:8008'\n\
          paths:\n  /comfy_gen_v2:\n    post:\n      summary: 通用 ComfyUI 生成 V2\n \
          \     description: 通用 ComfyUI 生成接口 V2\n      operationId: comfy_gen_v2\n\
          \      requestBody:\n        content:\n          application/json:\n   \
          \         schema:\n              $ref: '#/components/schemas/ComfyGenV2Req'\n\
          \        required: true\n      responses:\n        '200':\n          description:\
          \ Successful Response\n          content:\n            application/json:\n\
          \              schema:\n                type: object\n                title:\
          \ Response Comfy Gen V2 Comfy Gen V2 Post\n        '422':\n          description:\
          \ Validation Error\n          content:\n            application/json:\n\
          \              schema:\n                $ref: '#/components/schemas/HTTPValidationError'\n\
          components:\n  schemas:\n    ComfyGenV2Req:\n      properties:\n       \
          \ workflow_name:\n          type: string\n          title: Workflow Name\n\
          \          description: 工作流名称\n        prompt:\n          type: string\n\
          \          title: Prompt\n          description: 生成提示词\n        width:\n\
          \          type: integer\n          title: Width\n          description:\
          \ 生成图片宽度\n          default: 1024\n        height:\n          type: integer\n\
          \          title: Height\n          description: 生成图片高度\n          default:\
          \ 1024\n        urls:\n          anyOf:\n            - type: string\n  \
          \          - type: 'null'\n          title: Urls\n          description:\
          \ 输入图片URL,多个URL用|分隔\n        show_url:\n          type: string\n       \
          \   title: show_url\n          description: 是否显示链接\n      type: object\n\
          \      required:\n        - workflow_name\n        - prompt\n      title:\
          \ ComfyGenV2Req\n      description: 通用 ComfyUI 生成请求体 V2\n    HTTPValidationError:\n\
          \      properties:\n        detail:\n          items:\n            $ref:\
          \ '#/components/schemas/ValidationError'\n          type: array\n      \
          \    title: Detail\n      type: object\n      title: HTTPValidationError\n\
          \    ValidationError:\n      properties:\n        loc:\n          items:\n\
          \            anyOf:\n              - type: string\n              - type:\
          \ integer\n          type: array\n          title: Location\n        msg:\n\
          \          type: string\n          title: Message\n        type:\n     \
          \     type: string\n          title: Error Type\n      type: object\n  \
          \    required:\n        - loc\n        - msg\n        - type\n      title:\
          \ ValidationError"
        code_language: python3
        desc: ''
        outputs:
          msg:
            children: null
            type: string
          status:
            children: null
            type: string
          url:
            children: null
            type: string
        selected: false
        title: COMFY返回解析
        type: code
        variables:
        - value_selector:
          - '1748266385503'
          - text
          variable: arg1
      height: 53
      id: '1748267330350'
      position:
        x: 2717.3348120375153
        y: 709.4557807604625
      positionAbsolute:
        x: 2717.3348120375153
        y: 709.4557807604625
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '![ai]({{#1748267330350.url#}})'
        desc: ''
        selected: false
        title: 直接回复 6
        type: answer
        variables: []
      height: 104
      id: '1748267532398'
      position:
        x: 3049.4513345683326
        y: 695.397519495054
      positionAbsolute:
        x: 3049.4513345683326
        y: 695.397519495054
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        selected: false
        template: "{%- if arg1.startswith('谷歌') -%}\r\n{{- arg1[2:] -}}\r\n{%- else\
          \ -%}\r\n{{- arg1 -}}\r\n{%- endif -%}"
        title: 过滤谷歌
        type: template-transform
        variables:
        - value_selector:
          - sys
          - query
          variable: arg1
      height: 53
      id: '17482676623780'
      position:
        x: 1586.9675072927703
        y: 920.5277000537271
      positionAbsolute:
        x: 1586.9675072927703
        y: 920.5277000537271
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        memory:
          query_prompt_template: '用户绘图指令：{{#17482676623780.output#}}

            用户原始输入：{{#sys.query#}}'
          role_prefix:
            assistant: ''
            user: ''
          window:
            enabled: false
            size: 50
        model:
          completion_params: {}
          mode: chat
          name: gpt-4o-mini
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: 6a6fcea7-27b3-475a-89c0-e1ca6abc76b2
          role: system
          text: "你是一名专业的 AI绘画和提示词修改专家，你的任务是基于“用户绘图指令”，按照以下规则，分析提取/构建绘图所需的英文提示词。\n\n\
            如果用户提供了英文提示词，则直接使用，无需重新构建。否则按以下规则构建：\n\n1. **识别核心主体与意图：** 准确理解用户想画什么，抓住最关键的人物、事物或场景。\n\
            2. **细化主体特征：**\n   * **人物/动物：** 补充外貌（发型、五官、肤色）、神态表情、动作姿态、服装风格与细节、佩戴饰品。\n\
            \   * **景物/物品：** 补充形态、材质、纹理、颜色、所处环境。\n3. **描绘环境背景：** 设定主体所处的具体环境（室内、室外、自然风光、城市街景）或氛围背景（抽象色彩、光影效果），增加画面的故事感和深度。\n\
            4. **设定光影效果：** 明确光线来源、强度、色调（如：午后阳光、霓虹灯光、柔和月光、电影感光效），营造特定氛围。\n5. **确定构图与视角：**\
            \ 选择合适的视角（仰视、俯视、平视）和景别（特写、近景、远景），指导画面布局。\n6. **丰富色彩与细节：** 指定主色调或色彩搭配，添加能提升画面丰富度的细节元素（如：飘落的花瓣、远处的飞鸟、桌面上的小物件）。\n\
            7. **加入品质提升词：** 适时添加通用高品质词汇，如：“杰作”、“最佳质量”、“超精细”、“高细节”、“逼真”、“锐利对焦”、“8K”、“电影感”、“氛围感”。\n\
            8. **组合成流畅描述：** 将以上要素有机地组织起来，形成一段或多段清晰、具体、富有想象力的描述性文字，确保关键词用逗号分隔，使AI能够准确理解并执行。\n\
            9. 横/竖/方屏属性（必须，根据绘图意图判断横、竖、方屏属性）：\n- 横屏: 如桌面壁纸/风景等\n- 竖屏: 如手机壁纸/人像等\n\
            - 方屏: 如头像等\n- 如以上判断无果，则默认：方屏\n\n约束：最终输出且仅输出基于用户真实的绘图意图的英文绘图提示词，不要输出解释、说明等其他内容。"
        selected: false
        title: 谷歌英文绘图提示词生成
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1748268247017'
      position:
        x: 1890.6185391860638
        y: 869.2760497651395
      positionAbsolute:
        x: 1890.6185391860638
        y: 869.2760497651395
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 用于生成图像的文本提示
            ja_JP: 用于生成图像的文本提示
            pt_BR: 用于生成图像的文本提示
            zh_Hans: 用于生成图像的文本提示
          label:
            en_US: prompt
            ja_JP: prompt
            pt_BR: prompt
            zh_Hans: prompt
          llm_description: 用于生成图像的文本提示
          max: null
          min: null
          name: prompt
          options: []
          placeholder:
            en_US: 用于生成图像的文本提示
            ja_JP: 用于生成图像的文本提示
            pt_BR: 用于生成图像的文本提示
            zh_Hans: 用于生成图像的文本提示
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: gemini-2.0-flash-exp-image-generation
          form: llm
          human_description:
            en_US: 使用的模型名称
            ja_JP: 使用的模型名称
            pt_BR: 使用的模型名称
            zh_Hans: 使用的模型名称
          label:
            en_US: model
            ja_JP: model
            pt_BR: model
            zh_Hans: model
          llm_description: 使用的模型名称
          max: null
          min: null
          name: model
          options: []
          placeholder:
            en_US: 使用的模型名称
            ja_JP: 使用的模型名称
            pt_BR: 使用的模型名称
            zh_Hans: 使用的模型名称
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: default-api-key
          form: llm
          human_description:
            en_US: API密钥
            ja_JP: API密钥
            pt_BR: API密钥
            zh_Hans: API密钥
          label:
            en_US: api_key
            ja_JP: api_key
            pt_BR: api_key
            zh_Hans: api_key
          llm_description: API密钥
          max: null
          min: null
          name: api_key
          options: []
          placeholder:
            en_US: API密钥
            ja_JP: API密钥
            pt_BR: API密钥
            zh_Hans: API密钥
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          api_key: ''
          model: ''
          prompt: ''
        provider_id: b501817b-f63b-49c6-ac2e-c140250beee4
        provider_name: GEMINI_PIC
        provider_type: api
        selected: false
        title: generateImage
        tool_configurations: {}
        tool_description: 根据文本提示生成图像
        tool_label: generateImage
        tool_name: generateImage
        tool_parameters:
          api_key:
            type: mixed
            value: AIzaSyAZRTdUkx-H2ioMYBdwFoFvTg6qCaWTGXg
          model:
            type: mixed
            value: gemini-2.0-flash-preview-image-generation
          prompt:
            type: mixed
            value: '{{#1748268247017.text#}}'
        type: tool
      height: 53
      id: '1748268435484'
      position:
        x: 2198.6690043881667
        y: 862.7801214311513
      positionAbsolute:
        x: 2198.6690043881667
        y: 862.7801214311513
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1748268532210.result#}}'
        desc: ''
        selected: false
        title: 直接回复 7
        type: answer
        variables: []
      height: 104
      id: '1748268525877'
      position:
        x: 3034.0896129110456
        y: 824.9309564858206
      positionAbsolute:
        x: 3034.0896129110456
        y: 824.9309564858206
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        code: "def main(json_data: str) -> dict:\n    import json\n    data = json.loads(json_data)\n\
          \    \n    # 检查 success 和 data 是否存在\n    if data.get(\"success\") and data.get(\"\
          data\"):\n        # 遍历 data 数组，找到第一个图片信息\n        for item in data[\"data\"\
          ]:\n            if item[\"type\"] == \"image\":\n                filename\
          \ = item[\"filename\"]\n                url = item[\"url\"]\n          \
          \      markdown_result = f\"![{filename}]({url})\"\n                return\
          \ {\"result\": markdown_result, \"picture_url\": url}\n    \n    # 如果没有找到图片，返回默认值\n\
          \    return {\"result\": \"No valid image found\", \"picture_url\": None}"
        code_language: python3
        desc: ''
        outputs:
          picture_url:
            children: null
            type: string
          result:
            children: null
            type: string
        selected: false
        title: 谷歌绘图数据提取
        type: code
        variables:
        - value_selector:
          - '1748268435484'
          - text
          variable: arg1
      height: 53
      id: '1748268532210'
      position:
        x: 2551.0089132182616
        y: 819.0165527677615
      positionAbsolute:
        x: 2551.0089132182616
        y: 819.0165527677615
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: append
          value:
          - '1748268791133'
          - output
          variable_selector:
          - conversation
          - pic_url
          write_mode: over-write
        selected: false
        title: 变量赋值 3
        type: assigner
        version: '2'
      height: 87
      id: '1748268727385'
      position:
        x: 3832.964516880472
        y: 717.0256137495287
      positionAbsolute:
        x: 3832.964516880472
        y: 717.0256137495287
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        output_type: string
        selected: false
        title: 变量聚合器 3
        type: variable-aggregator
        variables:
        - - '1748267330350'
          - url
        - - '1748268532210'
          - picture_url
      height: 130
      id: '1748268791133'
      position:
        x: 3401.033141928177
        y: 702.96735248412
      positionAbsolute:
        x: 3401.033141928177
        y: 702.96735248412
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params:
            temperature: 0.1
          mode: chat
          name: qwen2.5-7b-instruct
          provider: langgenius/tongyi/tongyi
        prompt_template:
        - id: 0e217e9c-ddc5-41b7-b82f-a5e5c53e28af
          role: system
          text: 结合“用户输入的两次修图指令”，将用户修图意图总结成一句话翻译成英文输出，输出且仅输出翻译后的英文内容
        - id: 49aa5148-3e5e-439e-9568-5ee84aec486c
          role: user
          text: 用户输入的两次修图指令：{{#conversation.pics_prompts#}}
        selected: false
        title: 谷歌融图提示词翻译
        type: llm
        variables: []
        vision:
          enabled: false
      height: 89
      id: '1748430212628'
      position:
        x: 2960.403235439887
        y: 554.4245387906889
      positionAbsolute:
        x: 2960.403235439887
        y: 554.4245387906889
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        cases:
        - case_id: 'true'
          conditions:
          - comparison_operator: empty
            id: 8bc5fb0d-8113-4cba-a13e-825a7c83c5f2
            value: ''
            varType: string
            variable_selector:
            - conversation
            - pic1
          id: 'true'
          logical_operator: and
        desc: ''
        selected: false
        title: 条件分支 6
        type: if-else
      height: 125
      id: '1748431592747'
      position:
        x: 2590.8668775902975
        y: 507
      positionAbsolute:
        x: 2590.8668775902975
        y: 507
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: over-write
          value:
          - '1747119741130'
          - pic_url
          - output
          variable_selector:
          - conversation
          - pic1
          write_mode: over-write
        - input_type: variable
          operation: append
          value:
          - '1747119741130'
          - prompt
          - output
          variable_selector:
          - conversation
          - pics_prompts
          write_mode: over-write
        selected: false
        title: 变量赋值 4
        type: assigner
        version: '2'
      height: 115
      id: '1748432208560'
      position:
        x: 2927.5995388472297
        y: 419.7592670135408
      positionAbsolute:
        x: 2927.5995388472297
        y: 419.7592670135408
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: 请引用或直接发送第二张图片
        desc: ''
        selected: false
        title: 直接回复 8
        type: answer
        variables: []
      height: 101
      id: '1748434384561'
      position:
        x: 3279.856167085911
        y: 419.7592670135408
      positionAbsolute:
        x: 3279.856167085911
        y: 419.7592670135408
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        is_team_authorization: true
        output_schema: null
        paramSchemas:
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 用于编辑图像的文本提示
            ja_JP: 用于编辑图像的文本提示
            pt_BR: 用于编辑图像的文本提示
            zh_Hans: 用于编辑图像的文本提示
          label:
            en_US: prompt
            ja_JP: prompt
            pt_BR: prompt
            zh_Hans: prompt
          llm_description: 用于编辑图像的文本提示
          max: null
          min: null
          name: prompt
          options: []
          placeholder:
            en_US: 用于编辑图像的文本提示
            ja_JP: 用于编辑图像的文本提示
            pt_BR: 用于编辑图像的文本提示
            zh_Hans: 用于编辑图像的文本提示
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
            ja_JP: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
            pt_BR: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
            zh_Hans: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
          label:
            en_US: image
            ja_JP: image
            pt_BR: image
            zh_Hans: image
          llm_description: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
          max: null
          min: null
          name: image
          options: []
          placeholder:
            en_US: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
            ja_JP: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
            pt_BR: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
            zh_Hans: 第一张图像数据（支持URL格式或base64格式，系统自动识别）
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: null
          form: llm
          human_description:
            en_US: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
            ja_JP: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
            pt_BR: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
            zh_Hans: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
          label:
            en_US: image2
            ja_JP: image2
            pt_BR: image2
            zh_Hans: image2
          llm_description: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
          max: null
          min: null
          name: image2
          options: []
          placeholder:
            en_US: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
            ja_JP: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
            pt_BR: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
            zh_Hans: 第二张图像数据（可选，支持URL格式或base64格式，系统自动识别）
          precision: null
          required: false
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: gemini-2.0-flash-exp-image-generation
          form: llm
          human_description:
            en_US: 使用的模型名称
            ja_JP: 使用的模型名称
            pt_BR: 使用的模型名称
            zh_Hans: 使用的模型名称
          label:
            en_US: model
            ja_JP: model
            pt_BR: model
            zh_Hans: model
          llm_description: 使用的模型名称
          max: null
          min: null
          name: model
          options: []
          placeholder:
            en_US: 使用的模型名称
            ja_JP: 使用的模型名称
            pt_BR: 使用的模型名称
            zh_Hans: 使用的模型名称
          precision: null
          required: true
          scope: null
          template: null
          type: string
        - auto_generate: null
          default: default-api-key
          form: llm
          human_description:
            en_US: API密钥
            ja_JP: API密钥
            pt_BR: API密钥
            zh_Hans: API密钥
          label:
            en_US: api_key
            ja_JP: api_key
            pt_BR: api_key
            zh_Hans: api_key
          llm_description: API密钥
          max: null
          min: null
          name: api_key
          options: []
          placeholder:
            en_US: API密钥
            ja_JP: API密钥
            pt_BR: API密钥
            zh_Hans: API密钥
          precision: null
          required: true
          scope: null
          template: null
          type: string
        params:
          api_key: ''
          image: ''
          image2: ''
          model: ''
          prompt: ''
        provider_id: b501817b-f63b-49c6-ac2e-c140250beee4
        provider_name: GEMINI_PIC
        provider_type: api
        selected: false
        title: editImage
        tool_configurations: {}
        tool_description: 根据文本提示和一张或两张图像（支持URL或base64格式）编辑图像
        tool_label: editImage
        tool_name: editImage
        tool_parameters:
          api_key:
            type: mixed
            value: gemini-2.0-flash-preview-image-generation
          image:
            type: mixed
            value: '{{#conversation.pic1#}}'
          image2:
            type: mixed
            value: '{{#1747119741130.pic_url.output#}}'
          model:
            type: mixed
            value: gemini-2.0-flash-preview-image-generation
          prompt:
            type: mixed
            value: '{{#1748430212628.text#}}'
        type: tool
      height: 53
      id: '1748437483795'
      position:
        x: 3279.856167085911
        y: 549.5226650594562
      positionAbsolute:
        x: 3279.856167085911
        y: 549.5226650594562
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '![image]({{#1748437483795.text#}})'
        desc: ''
        selected: false
        title: 直接回复 9
        type: answer
        variables: []
      height: 104
      id: '1748437679409'
      position:
        x: 4275.018989094197
        y: 549.5226650594562
      positionAbsolute:
        x: 4275.018989094197
        y: 549.5226650594562
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        desc: ''
        items:
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - pic1
          write_mode: over-write
        - input_type: variable
          operation: clear
          value: ''
          variable_selector:
          - conversation
          - pics_prompts
          write_mode: over-write
        selected: false
        title: 变量赋值 5
        type: assigner
        version: '2'
      height: 115
      id: '1748441783383'
      position:
        x: 3740.834605453504
        y: 554.4245387906889
      positionAbsolute:
        x: 3740.834605453504
        y: 554.4245387906889
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -1683.495433906914
      y: -56.585651055094615
      zoom: 0.6469233192716143
