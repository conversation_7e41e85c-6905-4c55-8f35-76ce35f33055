app:
  description: ''
  icon: 🤖
  icon_background: '#FFEAD5'
  mode: advanced-chat
  name: 图片生成html,网页小游戏（1panel mcpsse）
  use_icon_as_answer_icon: false
dependencies:
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/siliconflow:0.0.11@616842815705a8e2ecf221f1c4b6956fede54d66915286cdbed97a5405fe821d
- current_identifier: null
  type: marketplace
  value:
    marketplace_plugin_unique_identifier: langgenius/openai_api_compatible:0.0.14@4e4b6c2e3080245d91e509ed44e62fb6e7886ffca9eede1d093ad47ad420d704
kind: app
version: 0.1.5
workflow:
  conversation_variables: []
  environment_variables: []
  features:
    file_upload:
      allowed_file_extensions: []
      allowed_file_types:
      - image
      allowed_file_upload_methods:
      - remote_url
      - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 500
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 100
        video_file_size_limit: 500
        workflow_file_upload_limit: 10
      image:
        enabled: false
        number_limits: 3
        transfer_methods:
        - local_file
        - remote_url
      number_limits: 1
    opening_statement: ''
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: false
    speech_to_text:
      enabled: false
    suggested_questions: []
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: false
      language: ''
      voice: ''
  graph:
    edges:
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: start
        targetType: question-classifier
      id: 1746243604396-source-1746243635872-target
      source: '1746243604396'
      sourceHandle: source
      target: '1746243635872'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: question-classifier
        targetType: agent
      id: 1746243635872-2-1746244131797-target
      source: '1746243635872'
      sourceHandle: '2'
      target: '1746244131797'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: agent
        targetType: answer
      id: 1746244131797-source-1746244242635-target
      source: '1746244131797'
      sourceHandle: source
      target: '1746244242635'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: question-classifier
        targetType: llm
      id: 1746243635872-1-1746245138243-target
      source: '1746243635872'
      sourceHandle: '1'
      target: '1746245138243'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInIteration: false
        isInLoop: false
        sourceType: llm
        targetType: agent
      id: 1746245138243-source-1746245802140-target
      source: '1746245138243'
      sourceHandle: source
      target: '1746245802140'
      targetHandle: target
      type: custom
      zIndex: 0
    - data:
        isInLoop: false
        sourceType: agent
        targetType: answer
      id: 1746245802140-source-answer-target
      source: '1746245802140'
      sourceHandle: source
      target: answer
      targetHandle: target
      type: custom
      zIndex: 0
    nodes:
    - data:
        desc: ''
        selected: false
        title: 开始
        type: start
        variables: []
      height: 53
      id: '1746243604396'
      position:
        x: 80
        y: 282
      positionAbsolute:
        x: 80
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1746245138243.text#}}

          {{#1746245802140.text#}}

          '
        desc: ''
        selected: false
        title: 直接回复
        type: answer
        variables: []
      height: 124
      id: answer
      position:
        x: 1520
        y: 205
      positionAbsolute:
        x: 1520
        y: 205
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        classes:
        - id: '1'
          name: 图片识别
        - id: '2'
          name: 小游戏
        desc: ''
        instructions: ''
        model:
          completion_params:
            temperature: 0.7
          mode: chat
          name: Pro/Qwen/Qwen2.5-VL-7B-Instruct
          provider: langgenius/siliconflow/siliconflow
        query_variable_selector:
        - '1746243604396'
        - sys.query
        selected: false
        title: 问题分类器
        topics: []
        type: question-classifier
        vision:
          configs:
            detail: high
            variable_selector:
            - sys
            - files
          enabled: false
      height: 171
      id: '1746243635872'
      position:
        x: 384
        y: 282
      positionAbsolute:
        x: 384
        y: 282
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        agent_parameters:
          instruction:
            type: constant
            value: 请根据用户输入的{{#sys.query#}} 先生成html页面代码，然后在调用edgeone-pages-mcp-server
              生成页面返回
          mcp_server:
            type: constant
            value: http://14.103.204.132:8004/edgeone-pages-mcp-server
          model:
            type: constant
            value:
              completion_params: {}
              mode: chat
              model: deepseek-V3
              model_type: llm
              provider: langgenius/volcengine_maas/volcengine_maas
              type: model-selector
          query:
            type: constant
            value: ' {{#sys.query#}}'
          tools:
            type: constant
            value:
            - enabled: true
              extra:
                description: ''
              parameters:
                localtime:
                  auto: 1
                  value: null
                timezone:
                  auto: 1
                  value: null
              provider_name: time
              schemas:
              - auto_generate: null
                default: null
                form: llm
                human_description:
                  en_US: localtime, such as 2024-1-1 0:0:0
                  ja_JP: localtime, such as 2024-1-1 0:0:0
                  pt_BR: localtime, such as 2024-1-1 0:0:0
                  zh_Hans: 本地时间, 比如2024-1-1 0:0:0
                label:
                  en_US: localtime
                  ja_JP: localtime
                  pt_BR: localtime
                  zh_Hans: 本地时间
                llm_description: null
                max: null
                min: null
                name: localtime
                options: []
                placeholder: null
                precision: null
                required: true
                scope: null
                template: null
                type: string
              - auto_generate: null
                default: Asia/Shanghai
                form: llm
                human_description:
                  en_US: Timezone, such as Asia/Shanghai
                  ja_JP: Timezone, such as Asia/Shanghai
                  pt_BR: Timezone, such as Asia/Shanghai
                  zh_Hans: 时区, 比如Asia/Shanghai
                label:
                  en_US: Timezone
                  ja_JP: Timezone
                  pt_BR: Timezone
                  zh_Hans: 时区
                llm_description: null
                max: null
                min: null
                name: timezone
                options: []
                placeholder: null
                precision: null
                required: false
                scope: null
                template: null
                type: string
              settings: {}
              tool_label: 获取时间戳
              tool_name: localtime_to_timestamp
              type: builtin
        agent_strategy_label: MCP FunctionCalling
        agent_strategy_name: function_calling
        agent_strategy_provider_name: hjlarry/agent/mcp_agent
        desc: ''
        output_schema: null
        plugin_unique_identifier: hjlarry/agent:0.0.1@f42a5a80b1c77fd0655c755b70ad08da47ceb1acc3638cf13a0eb9ed42b3a128
        selected: false
        title: Agent生成游戏HTML
        type: agent
      height: 197
      id: '1746244131797'
      position:
        x: 691
        y: 490
      positionAbsolute:
        x: 691
        y: 490
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        answer: '{{#1746244131797.text#}}'
        desc: ''
        selected: false
        title: 直接回复 2
        type: answer
        variables: []
      height: 105
      id: '1746244242635'
      position:
        x: 995
        y: 490
      positionAbsolute:
        x: 995
        y: 490
      selected: true
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        context:
          enabled: false
          variable_selector: []
        desc: ''
        model:
          completion_params: {}
          mode: chat
          name: gemini-2.5-flash-preview-04-17
          provider: langgenius/openai_api_compatible/openai_api_compatible
        prompt_template:
        - id: abf09e5e-c8f2-444c-8587-2c8928623c84
          role: system
          text: 请根据用户上传的图片内容信息，保持内容的颜色布局和风格，注意CSS表和表格内容，生成 html页面代码
        - role: user
          text: ''
        selected: false
        title: 图片识别生成html
        type: llm
        variables: []
        vision:
          configs:
            detail: high
            variable_selector:
            - sys
            - files
          enabled: true
      height: 89
      id: '1746245138243'
      position:
        x: 733.8334709604239
        y: 205
      positionAbsolute:
        x: 733.8334709604239
        y: 205
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    - data:
        agent_parameters:
          instruction:
            type: constant
            value: 请根据{{#1746245138243.text#}} 先生成html页面代码，然后在调用edgeone-pages-mcp-server
              生成页面返回
          mcp_server:
            type: constant
            value: http://14.103.204.132:8004/edgeone-pages-mcp-server
          model:
            type: constant
            value:
              completion_params: {}
              mode: chat
              model: deepseek-V3
              model_type: llm
              provider: langgenius/volcengine_maas/volcengine_maas
              type: model-selector
          query:
            type: constant
            value: '{{#sys.query#}}'
          tools:
            type: constant
            value:
            - enabled: true
              extra:
                description: ''
              parameters:
                localtime:
                  auto: 1
                  value: null
                timezone:
                  auto: 1
                  value: null
              provider_name: time
              schemas:
              - auto_generate: null
                default: null
                form: llm
                human_description:
                  en_US: localtime, such as 2024-1-1 0:0:0
                  ja_JP: localtime, such as 2024-1-1 0:0:0
                  pt_BR: localtime, such as 2024-1-1 0:0:0
                  zh_Hans: 本地时间, 比如2024-1-1 0:0:0
                label:
                  en_US: localtime
                  ja_JP: localtime
                  pt_BR: localtime
                  zh_Hans: 本地时间
                llm_description: null
                max: null
                min: null
                name: localtime
                options: []
                placeholder: null
                precision: null
                required: true
                scope: null
                template: null
                type: string
              - auto_generate: null
                default: Asia/Shanghai
                form: llm
                human_description:
                  en_US: Timezone, such as Asia/Shanghai
                  ja_JP: Timezone, such as Asia/Shanghai
                  pt_BR: Timezone, such as Asia/Shanghai
                  zh_Hans: 时区, 比如Asia/Shanghai
                label:
                  en_US: Timezone
                  ja_JP: Timezone
                  pt_BR: Timezone
                  zh_Hans: 时区
                llm_description: null
                max: null
                min: null
                name: timezone
                options: []
                placeholder: null
                precision: null
                required: false
                scope: null
                template: null
                type: string
              settings: {}
              tool_label: 获取时间戳
              tool_name: localtime_to_timestamp
              type: builtin
        agent_strategy_label: MCP FunctionCalling
        agent_strategy_name: function_calling
        agent_strategy_provider_name: hjlarry/agent/mcp_agent
        desc: ''
        output_schema: null
        plugin_unique_identifier: hjlarry/agent:0.0.1@f42a5a80b1c77fd0655c755b70ad08da47ceb1acc3638cf13a0eb9ed42b3a128
        selected: false
        title: 图片生成html
        type: agent
      height: 197
      id: '1746245802140'
      position:
        x: 1165
        y: 197
      positionAbsolute:
        x: 1165
        y: 197
      selected: false
      sourcePosition: right
      targetPosition: left
      type: custom
      width: 244
    viewport:
      x: -311.09461984474547
      y: 29.615856578229568
      zoom: 0.8572439828530728
